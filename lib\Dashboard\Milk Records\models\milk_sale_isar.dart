import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'milk_sale_isar.g.dart';

@collection
class MilkSaleIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String saleId = '';

  double quantity = 0;

  double price = 0;

  double total = 0;

  String buyer = '';

  @Index()
  DateTime date = DateTime.now();

  String? notes;

  String? paymentStatus;

  String? paymentMethod;

  DateTime createdAt = DateTime.now();

  DateTime updatedAt = DateTime.now();

  MilkSaleIsar();

  /// Generate a unique sale ID for milk sales
  static String generateSaleId() {
    return 'sale_${const Uuid().v4()}';
  }

  factory MilkSaleIsar.create({
    String? saleId,
    required double quantity,
    required double price,
    required double total,
    required String buyer,
    required DateTime date,
    String? notes,
    String? paymentStatus,
    String? paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MilkSaleIsar()
      ..saleId = saleId ?? generateSaleId()
      ..quantity = quantity
      ..price = price
      ..total = total
      ..buyer = buyer
      ..date = date
      ..notes = notes
      ..paymentStatus = paymentStatus
      ..paymentMethod = paymentMethod
      ..createdAt = createdAt ?? DateTime.now()
      ..updatedAt = updatedAt ?? DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': saleId,
      'quantity': quantity,
      'price': price,
      'total': total,
      'buyer': buyer,
      'date': date.toIso8601String(),
      'notes': notes,
      'paymentStatus': paymentStatus,
      'paymentMethod': paymentMethod,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory MilkSaleIsar.fromMap(Map<String, dynamic> map) {
    return MilkSaleIsar()
      ..saleId = map['id'] as String
      ..quantity = map['quantity'] as double
      ..price = map['price'] as double
      ..total = map['total'] as double
      ..buyer = map['buyer'] as String
      ..date = DateTime.parse(map['date'] as String)
      ..notes = map['notes'] as String?
      ..paymentStatus = map['paymentStatus'] as String?
      ..paymentMethod = map['paymentMethod'] as String?
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'] as String)
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'] as String)
          : DateTime.now();
  }

  factory MilkSaleIsar.fromJson(Map<String, dynamic> json) => MilkSaleIsar.fromMap(json);

  MilkSaleIsar copyWith({
    String? saleId,
    double? quantity,
    double? price,
    double? total,
    String? buyer,
    DateTime? date,
    String? notes,
    String? paymentStatus,
    String? paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MilkSaleIsar()
      ..saleId = saleId ?? this.saleId
      ..quantity = quantity ?? this.quantity
      ..price = price ?? this.price
      ..total = total ?? this.total
      ..buyer = buyer ?? this.buyer
      ..date = date ?? this.date
      ..notes = notes ?? this.notes
      ..paymentStatus = paymentStatus ?? this.paymentStatus
      ..paymentMethod = paymentMethod ?? this.paymentMethod
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? this.updatedAt;
  }
} 