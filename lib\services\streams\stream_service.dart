import 'dart:async';
import 'package:logging/logging.dart';
// import '../constants/record_keys.dart';
// import '../services/notification_service.dart';

// Define RecordKeys enum here to replace the missing import
class RecordKeys {
  static const String breedingRecords = 'breedingRecords';
  static const String pregnancyRecords = 'pregnancyRecords';
  static const String healthRecords = 'healthRecords';
  static const String milkRecords = 'milkRecords';
  static const String eventRecords = 'eventRecords';
  static const String cattleRecords = 'cattleRecords';
  static const String farmRecords = 'farmRecords';
  static const String categoryRecords = 'categoryRecords';
  static const String animalTypeRecords = 'animalTypeRecords';
  static const String breedRecords = 'breedRecords';
  static const String deliveryRecords = 'deliveryRecords';
  static const String transactionRecords = 'transactionRecords';
  static const String vaccinationRecords = 'vaccinationRecords';
  static const String treatmentRecords = 'treatmentRecords';
  static const String medicationRecords = 'medicationRecords';
}

class StreamService {
  static final Logger _logger = Logger('StreamService');
  // final NotificationService _notificationService;

  // Stream controllers for different record types
  final StreamController<Map<String, dynamic>> _breedingStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _pregnancyStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _healthStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _milkStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _eventStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _cattleStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _farmStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _categoryStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _animalTypeStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _breedStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _deliveryStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _transactionStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _vaccinationStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _treatmentStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _medicationStreamController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Public stream getters
  Stream<Map<String, dynamic>> get breedingStream =>
      _breedingStreamController.stream;
  Stream<Map<String, dynamic>> get pregnancyStream =>
      _pregnancyStreamController.stream;
  Stream<Map<String, dynamic>> get healthStream =>
      _healthStreamController.stream;
  Stream<Map<String, dynamic>> get milkStream => _milkStreamController.stream;
  Stream<Map<String, dynamic>> get eventStream => _eventStreamController.stream;
  Stream<Map<String, dynamic>> get cattleStream =>
      _cattleStreamController.stream;
  Stream<Map<String, dynamic>> get farmStream => _farmStreamController.stream;
  Stream<Map<String, dynamic>> get categoryStream =>
      _categoryStreamController.stream;
  Stream<Map<String, dynamic>> get animalTypeStream =>
      _animalTypeStreamController.stream;
  Stream<Map<String, dynamic>> get breedStream => _breedStreamController.stream;
  Stream<Map<String, dynamic>> get deliveryStream =>
      _deliveryStreamController.stream;
  Stream<Map<String, dynamic>> get transactionStream =>
      _transactionStreamController.stream;
  Stream<Map<String, dynamic>> get vaccinationStream =>
      _vaccinationStreamController.stream;
  Stream<Map<String, dynamic>> get treatmentStream =>
      _treatmentStreamController.stream;
  Stream<Map<String, dynamic>> get medicationStream =>
      _medicationStreamController.stream;

  // StreamService(this._notificationService) {
  StreamService() {
    _initialize();
  }

  void _initialize() {
    _logger.info('Initializing StreamService');
    // _setupNotificationListeners();
  }

  void _handleNotification(String recordKey, String action, String cattleId,
      Map<String, dynamic> data) {
    try {
      final notificationData = {
        'action': action,
        'cattleId': cattleId,
        'data': data,
      };

      switch (recordKey) {
        case RecordKeys.breedingRecords:
          _breedingStreamController.add(notificationData);
          break;
        case RecordKeys.pregnancyRecords:
          _pregnancyStreamController.add(notificationData);
          break;
        case RecordKeys.healthRecords:
          _healthStreamController.add(notificationData);
          break;
        case RecordKeys.milkRecords:
          _milkStreamController.add(notificationData);
          break;
        case RecordKeys.eventRecords:
          _eventStreamController.add(notificationData);
          break;
        case RecordKeys.cattleRecords:
          _cattleStreamController.add(notificationData);
          break;
        case RecordKeys.farmRecords:
          _farmStreamController.add(notificationData);
          break;
        case RecordKeys.categoryRecords:
          _categoryStreamController.add(notificationData);
          break;
        case RecordKeys.animalTypeRecords:
          _animalTypeStreamController.add(notificationData);
          break;
        case RecordKeys.breedRecords:
          _breedStreamController.add(notificationData);
          break;
        case RecordKeys.deliveryRecords:
          _deliveryStreamController.add(notificationData);
          break;
        case RecordKeys.transactionRecords:
          _transactionStreamController.add(notificationData);
          break;
        case RecordKeys.vaccinationRecords:
          _vaccinationStreamController.add(notificationData);
          break;
        case RecordKeys.treatmentRecords:
          _treatmentStreamController.add(notificationData);
          break;
        case RecordKeys.medicationRecords:
          _medicationStreamController.add(notificationData);
          break;
        default:
          _logger.warning('Unhandled notification for record key: $recordKey');
      }
    } catch (e) {
      _logger.severe('Error handling notification', e);
    }
  }

  // Public method to handle notifications directly without the notification service
  void handleNotification(String recordKey, String action, String cattleId,
      Map<String, dynamic> data) {
    _handleNotification(recordKey, action, cattleId, data);
  }

  // Notification methods for different record types
  void notifyBreedingChange(Map<String, dynamic> data) {
    _logger.info('Notifying breeding change: ${data['action']} for cattle ${data['cattleId']} with ID ${data['id'] ?? data['recordId']}');
    _breedingStreamController.add(data);

    // Also notify cattle change for breeding status updates
    if (data['cattleId'] != null) {
      _logger.info('Also notifying cattle change due to breeding update');
      notifyCattleChange({
        'action': 'update',
        'cattleId': data['cattleId'],
      });
    }
  }

  void notifyPregnancyChange(Map<String, dynamic> data) {
    _logger.info('Notifying pregnancy change: ${data['action']} for cattle ${data['cattleId']} with ID ${data['id'] ?? data['recordId']}');
    _pregnancyStreamController.add(data);

    // Also notify cattle change for pregnancy status updates
    if (data['cattleId'] != null) {
      _logger.info('Also notifying cattle change due to pregnancy update');
      notifyCattleChange({
        'action': 'update',
        'cattleId': data['cattleId'],
      });
    }
  }

  void notifyHealthChange(Map<String, dynamic> data) {
    _healthStreamController.add(data);
  }

  void notifyMilkChange(Map<String, dynamic> data) {
    _milkStreamController.add(data);
  }

  void notifyEventChange(Map<String, dynamic> data) {
    _eventStreamController.add(data);
  }

  void notifyCattleChange(Map<String, dynamic> data) {
    _cattleStreamController.add(data);
  }

  void notifyFarmChange(Map<String, dynamic> data) {
    _farmStreamController.add(data);
  }

  void notifyDeliveryChange(Map<String, dynamic> data) {
    _deliveryStreamController.add(data);
  }

  // Get a stream for a specific record type
  Stream<Map<String, dynamic>> getStream(String recordKey) {
    switch (recordKey) {
      case RecordKeys.breedingRecords:
        return breedingStream;
      case RecordKeys.pregnancyRecords:
        return pregnancyStream;
      case RecordKeys.healthRecords:
        return healthStream;
      case RecordKeys.milkRecords:
        return milkStream;
      case RecordKeys.eventRecords:
        return eventStream;
      case RecordKeys.cattleRecords:
        return cattleStream;
      case RecordKeys.farmRecords:
        return farmStream;
      case RecordKeys.categoryRecords:
        return categoryStream;
      case RecordKeys.animalTypeRecords:
        return animalTypeStream;
      case RecordKeys.breedRecords:
        return breedStream;
      case RecordKeys.deliveryRecords:
        return deliveryStream;
      case RecordKeys.transactionRecords:
        return transactionStream;
      case RecordKeys.vaccinationRecords:
        return vaccinationStream;
      case RecordKeys.treatmentRecords:
        return treatmentStream;
      default:
        _logger.warning('No stream available for record key: $recordKey');
        // Return an empty stream as fallback
        return const Stream<Map<String, dynamic>>.empty();
    }
  }

  // Combine multiple streams into a single stream
  Stream<Map<String, dynamic>> combineStreams(List<String> recordKeys) {
    if (recordKeys.isEmpty) {
      return const Stream<Map<String, dynamic>>.empty();
    }

    if (recordKeys.length == 1) {
      return getStream(recordKeys.first);
    }

    final streams = recordKeys.map((key) => getStream(key)).toList();
    return StreamGroup.merge(streams);
  }

  // Dispose all stream controllers
  void dispose() {
    _logger.info('Disposing StreamService');
    _breedingStreamController.close();
    _pregnancyStreamController.close();
    _healthStreamController.close();
    _milkStreamController.close();
    _eventStreamController.close();
    _cattleStreamController.close();
    _farmStreamController.close();
    _categoryStreamController.close();
    _animalTypeStreamController.close();
    _breedStreamController.close();
    _deliveryStreamController.close();
    _transactionStreamController.close();
    _vaccinationStreamController.close();
    _treatmentStreamController.close();
    _medicationStreamController.close();
  }
}

// Helper class to merge multiple streams
class StreamGroup {
  static Stream<T> merge<T>(List<Stream<T>> streams) {
    final controller = StreamController<T>.broadcast();

    final subscriptions = <StreamSubscription>[];

    for (final stream in streams) {
      final subscription = stream.listen(
        (data) => controller.add(data),
        onError: (error, stackTrace) => controller.addError(error, stackTrace),
      );
      subscriptions.add(subscription);
    }

    controller.onCancel = () {
      for (final subscription in subscriptions) {
        subscription.cancel();
      }
    };

    return controller.stream;
  }
}
