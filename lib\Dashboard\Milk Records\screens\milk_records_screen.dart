import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_service.dart';
import '../services/milk_handler.dart';
import '../dialogs/milk_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Cattle/screens/cattle_detail_screen.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/empty_state.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'package:get_it/get_it.dart';
import 'package:logging/logging.dart';
import '../../../services/streams/stream_service.dart';
import '../../widgets/reusable_filter_search.dart';
import '../../widgets/filter_models.dart';
import '../../widgets/filter_utils.dart';

// Wrapper class to navigate directly to milk tab and records tab
class CattleDetailScreenWithMilkTab extends StatelessWidget {
  final CattleIsar cattle;
  final BreedCategoryIsar breed;
  final AnimalTypeIsar animalType;
  final Function(CattleIsar) onCattleUpdated;

  const CattleDetailScreenWithMilkTab({
    Key? key,
    required this.cattle,
    required this.breed,
    required this.animalType,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // For female cattle, the Milk tab is at index 4 (Overview=0, Family Tree=1, Breeding=2, Health=3, Milk=4, Events=5)
    final isFemale = (cattle.gender?.toLowerCase() ?? '') == 'female';
    final milkTabIndex = isFemale ? 4 : null; // Only set if female cattle

    return CattleDetailScreen(
      existingCattle: cattle,
      businessId: cattle.businessId ?? '',
      onCattleUpdated: onCattleUpdated,
      initialTabIndex: milkTabIndex, // Navigate to Milk tab
      milkRecordsSelected: true, // Open Records sub-tab within Milk tab
    );
  }
}

class MilkRecordsScreen extends StatefulWidget {
  const MilkRecordsScreen({Key? key}) : super(key: key);

  @override
  State<MilkRecordsScreen> createState() => _MilkRecordsScreenState();
}

class _MilkRecordsScreenState extends State<MilkRecordsScreen> {
  final MilkService _milkService = MilkService();
  final MilkHandler _milkHandler = MilkHandler.instance;
  final CattleHandler _cattleHandler = CattleHandler.instance;
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  List<MilkRecordIsar> _records = [];
  List<MilkRecordIsar> _filteredRecords = [];
  Map<String, CattleIsar> _cattleMap = {};
  bool _isLoading = true;
  Timer? _refreshTimer;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  // Filter state using reusable component
  late FilterState _filterState;
  List<AnimalTypeIsar> _animalTypes = [];
  List<CattleIsar> _filteredCattle = [];
  Map<String, String> _animalTypeIdToName = {};
  final _logger = Logger('MilkRecordsScreen');
  final TextEditingController _searchController = TextEditingController();

  // Stream subscription for real-time updates
  StreamSubscription<Map<String, dynamic>>? _milkStreamSubscription;



  @override
  void initState() {
    super.initState();
    _filterState = FilterState();
    _initializeData();
    _subscribeToMilkUpdates();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _milkStreamSubscription?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    try {
      setState(() => _isLoading = true);

      // Load all required data using Isar handlers directly
      final animalTypes = await _farmSetupHandler.getAllAnimalTypes();
      final allCattle = await _cattleHandler.getAllCattle();

      // Create maps for efficient lookups
      final cattleMap = {
        for (var cattle in allCattle) cattle.tagId ?? '': cattle
      };

      // Create animal type mapping
      final animalTypeIdToName = {
        for (var type in animalTypes) type.businessId ?? '': type.name ?? ''
      };

      // Update filtered cattle list
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: allCattle,
        selectedAnimalType: _filterState.selectedAnimalType,
        animalTypeIdToName: animalTypeIdToName,
        genderFilter: 'female', // Milk module shows only female cattle
      );

      // Load milk records using MilkHandler directly
      final records = await _milkHandler.getAllMilkRecords();

      // Sort records by date (with null check)
      records.sort((a, b) {
        if (a.date == null) return 1;
        if (b.date == null) return -1;
        return b.date!.compareTo(a.date!);
      });

      // Verify data integrity
      for (var record in records) {
        final cattle = cattleMap[record.cattleTagId];
        if (cattle == null) {
          _logger.warning('No cattle found for tag ID: ${record.cattleTagId}');
        }
      }

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _animalTypes = animalTypes;
          _animalTypeIdToName = animalTypeIdToName;
          _records = records;
          _isLoading = false;
        });
      }

      // Apply initial filtering
      _filterRecords();

      // Remove periodic refresh - now using real-time updates
      // _setupPeriodicRefresh();
    } catch (e, stackTrace) {
      _logger.severe('Error loading milk records data', e, stackTrace);
      if (mounted) {
        setState(() => _isLoading = false);
        MilkMessageUtils.showError(context, 'Error loading milk records: $e');
      }
    }
  }

  /// Subscribe to milk record updates for real-time UI updates
  void _subscribeToMilkUpdates() {
    final streamService = GetIt.instance<StreamService>();

    // Subscribe to milk record updates
    _milkStreamSubscription = streamService.milkStream.listen((event) {
      _logger.info('Milk record stream event: ${event['action']}');
      if (mounted) {
        _refreshData(); // Refresh data when milk records change
      }
    }, onError: (error) {
      _logger.severe('Error in milk record stream: $error');
    });
  }

  void _setupPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _refreshData();
    });
  }

  Future<void> _refreshData() async {
    if (!mounted) return;
    try {
      await _initializeData();
    } catch (e) {
      _logger.severe('Error refreshing milk records data', e);
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error refreshing data: $e');
      }
    }
  }

  void _onFilterChanged(FilterState newFilterState) {
    setState(() {
      _filterState = newFilterState;

      // Update filtered cattle when animal type changes
      if (_filterState.selectedAnimalType != newFilterState.selectedAnimalType) {
        _filteredCattle = FilterUtils.filterCattle(
          allCattle: _cattleMap.values.toList(),
          selectedAnimalType: newFilterState.selectedAnimalType,
          animalTypeIdToName: _animalTypeIdToName,
          genderFilter: 'female', // Milk module shows only female cattle
        );

        // Clear cattle selection if it's no longer valid
        if (!FilterUtils.isCattleIdValid(newFilterState.selectedCattleId, _filteredCattle)) {
          _filterState = _filterState.copyWith(selectedCattleId: 'All');
        }
      }
    });

    _filterRecords();
  }

  void _onSearchChanged(String searchQuery) {
    // This is called immediately when search text changes
    // The actual filtering happens in _onFilterChanged
  }

  void _onClearFilters() {
    setState(() {
      _filterState.clearAll();
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: _cattleMap.values.toList(),
        selectedAnimalType: 'All',
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: 'female',
      );
    });
    _filterRecords();
  }

  void _filterRecords() {
    _filteredRecords = FilterUtils.filterRecords<MilkRecordIsar>(
      records: _records,
      filterState: _filterState,
      cattleMap: _cattleMap,
      animalTypeIdToName: _animalTypeIdToName,
      getCattleId: (record) => record.cattleTagId ?? '',
      getRecordDate: (record) => record.date,
      getSearchableFields: (record) => [
        record.morningAmount?.toString() ?? '',
        record.eveningAmount?.toString() ?? '',
        record.notes ?? '',
        record.date != null ? DateFormat('yyyy-MM-dd').format(record.date!) : '',
      ],
    );

    // Sort by date (newest first)
    _filteredRecords.sort((a, b) {
      if (a.date == null) return 1;
      if (b.date == null) return -1;
      return b.date!.compareTo(a.date!);
    });

    setState(() {});
  }

  Future<void> _showAddRecordDialog([MilkRecordIsar? record]) async {
    final result = await showDialog<MilkRecordIsar>(
      context: context,
      builder: (context) => MilkFormDialog(
        record: record,
        cattleTagId: record?.cattleTagId,
      ),
    );

    if (result != null) {
      try {
        if (record != null) {
          await _milkService.updateMilkRecord(result);
        } else {
          await _milkService.addMilkRecord(result);
        }
        await _refreshData();
      } catch (e) {
        if (mounted) {
          MilkMessageUtils.showError(context,
              'Error ${record != null ? 'updating' : 'adding'} record: $e');
        }
      }
    }
  }

  Future<void> _deleteMilkRecord(MilkRecordIsar record) async {
    try {
      await _milkService.deleteMilkRecord(record.id);
      if (mounted) {
        MilkMessageUtils.showSuccess(context, MilkMessageUtils.milkRecordDeleted());
      }
      await _refreshData();
    } catch (e) {
      _logger.severe('Error deleting milk record', e);
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error deleting milk record: $e');
      }
    }
  }

  void _confirmDeleteRecord(MilkRecordIsar record) {
    final cattle = _cattleMap[record.cattleTagId];
    final cattleName = cattle?.name ?? 'Unknown Cattle';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Record'),
        content: Text(
            'Are you sure you want to delete this milk record for $cattleName from ${DateFormat('MMMM dd, yyyy').format(record.date ?? DateTime.now())}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteMilkRecord(record);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }





  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Records'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Reusable Filter and Search Component
                ReusableFilterSearch(
                  config: FilterConfig.milk,
                  filterState: _filterState,
                  filterData: FilterData(
                    cattleMap: _cattleMap,
                    animalTypes: _animalTypes,
                    animalTypeIdToName: _animalTypeIdToName,
                    filteredCattle: _filteredCattle,
                  ),
                  searchController: _searchController,
                  onFilterChanged: _onFilterChanged,
                  onSearchChanged: _onSearchChanged,
                  onClearFilters: _onClearFilters,
                  totalRecords: _records.length,
                  filteredRecords: _filteredRecords.length,
                ),
                Expanded(
                  child: _filteredRecords.isEmpty
                      ? MilkEmptyState(
                          icon: _records.isEmpty
                              ? Icons.water_drop_outlined
                              : Icons.filter_alt_off_outlined,
                          message: _records.isEmpty
                              ? 'No Milk Records'
                              : 'No Records Found',
                          subtitle: _records.isEmpty
                              ? 'Start by adding your first milk record'
                              : 'No records match your current filters',
                          color: const Color(0xFF1976D2), // Blue for records
                          action: _records.isEmpty
                              ? ElevatedButton.icon(
                                  onPressed: _showAddRecordDialog,
                                  icon: const Icon(Icons.add),
                                  label: const Text('Add First Record'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF1976D2),
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                )
                              : ElevatedButton.icon(
                                  onPressed: _onClearFilters,
                                  icon: const Icon(Icons.clear_all),
                                  label: const Text('Clear Filters'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF1976D2),
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.only(top: 8, bottom: 88),
                          itemCount: _filteredRecords.length,
                          itemBuilder: (context, index) {
                            final record = _filteredRecords[index];
                            return _buildRecordCard(record);
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddRecordDialog,
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _openEditDialog(MilkRecordIsar record) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => MilkFormDialog(
        record: record,
        cattleTagId: record.cattleTagId,
      ),
    );

    if (result == true && mounted) {
      _refreshData();
    }
  }

  Widget _buildMilkQuantityItem(
    IconData icon,
    String label,
    String value,
    MaterialColor color, {
    bool isTotal = false,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: isTotal
            ? BoxDecoration(
                color: color.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: color.shade200),
              )
            : null,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 14, color: color.shade700),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isTotal ? color.shade700 : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordCard(MilkRecordIsar record) {
    final cattle = _cattleMap[record.cattleTagId];
    final cattleName = cattle?.name ?? 'Unknown Cattle';

    return GestureDetector(
      onTap: () => _navigateToCattleMilkTab(record.cattleTagId),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 2.0,
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.blue.shade50,
                        radius: 24,
                        child: Icon(
                          Icons.water_drop,
                          color: Colors.blue.shade700,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('MMMM dd, yyyy')
                                  .format(record.date ?? DateTime.now()),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '$cattleName (${record.cattleTagId})',
                              style: const TextStyle(
                                fontSize: 15,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildMilkQuantityItem(
                        Icons.wb_sunny_outlined,
                        'Morning',
                        '${record.morningAmount?.toStringAsFixed(1) ?? 'N/A'} L',
                        Colors.blue, // Use blue instead of orange
                      ),
                      _buildMilkQuantityItem(
                        Icons.nightlight_outlined,
                        'Evening',
                        '${record.eveningAmount?.toStringAsFixed(1) ?? 'N/A'} L',
                        Colors.indigo,
                      ),
                      _buildMilkQuantityItem(
                        Icons.local_drink_outlined,
                        'Total',
                        '${record.totalYield.toStringAsFixed(1)} L',
                        Colors.green,
                        isTotal: true,
                      ),
                    ],
                  ),
                  if (record.notes != null && record.notes!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Divider(),
                    const SizedBox(height: 4),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(Icons.note, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            record.notes!,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.grey,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () => _openEditDialog(record),
                    color: Colors.blue,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Edit Record',
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed: () => _confirmDeleteRecord(record),
                    color: Colors.red,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Delete Record',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCattleMilkTab(String? tagId) async {
    if (tagId == null) return;

    final cattle = _cattleMap[tagId];
    if (cattle == null) {
      if (mounted) {
        MilkMessageUtils.showError(context, 'Cattle not found');
      }
      return;
    }

    try {
      // Get breed and animal type information
      final breedData = await _farmSetupHandler.getAllBreedCategories();
      final animalTypeData = await _farmSetupHandler.getAllAnimalTypes();

      // Find matching breed and animal type for this cattle
      final breed = breedData.firstWhere(
        (b) => b.businessId == cattle.breedId,
        orElse: () => breedData.first,
      );

      final animalType = animalTypeData.firstWhere(
        (t) => t.businessId == cattle.animalTypeId,
        orElse: () => animalTypeData.first,
      );

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CattleDetailScreenWithMilkTab(
              cattle: cattle,
              breed: breed,
              animalType: animalType,
              onCattleUpdated: (updatedCattle) async {
                await _cattleHandler.updateCattle(updatedCattle);
                _refreshData();
              },
            ),
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error navigating to cattle details', e);
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error: $e');
      }
    }
  }
}
