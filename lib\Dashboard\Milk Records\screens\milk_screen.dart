import 'package:flutter/material.dart';
import 'milk_records_screen.dart';
import 'milk_summary_screen.dart';
import 'milk_alerts_screen.dart';
import 'milk_sales_screen.dart';

class MilkScreen extends StatelessWidget {
  const MilkScreen({super.key});

  @override
  Widget build(BuildContext context) {
    const mainColor = Color(0xFF2E7D32);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Milk Management',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: mainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildNavigationCard(
              context,
              'Records',
              Icons.list_alt,
              'View and manage milk production records',
              Colors.blue,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MilkRecordsScreen(),
                ),
              ),
            ),
            _buildNavigationCard(
              context,
              'Milk Summary',
              Icons.analytics,
              'View milk analytics and trends',
              Colors.green,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MilkSummaryScreen(),
                ),
              ),
            ),
            _buildNavigationCard(
              context,
              'Sales',
              Icons.point_of_sale,
              'Track milk sales and revenue',
              Colors.orange,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MilkSalesScreen(),
                ),
              ),
            ),
            _buildNavigationCard(
              context,
              'Milk Alerts',
              Icons.notifications,
              'View milk production alerts and notifications',
              Colors.red,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MilkAlertsScreen(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationCard(
    BuildContext context,
    String title,
    IconData icon,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(51),
          radius: 24,
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }
}
