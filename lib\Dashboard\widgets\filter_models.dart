import '../Cattle/models/cattle_isar.dart';

/// Configuration class for the reusable filter and search component
class FilterConfig {
  /// Whether to show the animal type filter dropdown
  final bool showAnimalTypeFilter;
  
  /// Whether to show the cattle filter dropdown
  final bool showCattleFilter;
  
  /// Whether to show the date range filter dropdown
  final bool showDateRangeFilter;
  
  /// Custom date range options (if null, uses default options)
  final List<String>? customDateRangeOptions;
  
  /// Search hint text
  final String searchHintText;
  
  /// Whether to filter cattle by gender (e.g., only female for breeding)
  final String? cattleGenderFilter;
  
  /// Custom search fields to include in search (beyond cattle name and tag ID)
  final List<String> additionalSearchFields;

  /// Whether to show active filters chips (deprecated - not used in breeding style)
  final bool showActiveFilters;

  /// Whether to show clear all filters button
  final bool showClearAllButton;

  /// Whether to show record count next to clear filters button
  final bool showRecordCount;

  const FilterConfig({
    this.showAnimalTypeFilter = true,
    this.showCattleFilter = true,
    this.showDateRangeFilter = true,
    this.customDateRangeOptions,
    this.searchHintText = 'Search by cattle name or tag ID',
    this.cattleGenderFilter,
    this.additionalSearchFields = const [],
    this.showActiveFilters = false, // Deprecated - breeding style doesn't use chips
    this.showClearAllButton = true,
    this.showRecordCount = true,
  });

  /// Default configuration for breeding module
  static const breeding = FilterConfig(
    cattleGenderFilter: 'female',
    searchHintText: 'Search by cattle name or tag ID',
    showActiveFilters: false, // Breeding style doesn't use filter chips
  );

  /// Default configuration for health module
  static const health = FilterConfig(
    searchHintText: 'Search by cattle name or tag ID',
    showActiveFilters: false, // Breeding style doesn't use filter chips
  );

  /// Default configuration for milk module
  static const milk = FilterConfig(
    cattleGenderFilter: 'female',
    searchHintText: 'Search by cattle name or tag ID',
    showActiveFilters: false, // Breeding style doesn't use filter chips
  );
}

/// Filter state class to hold all filter values
class FilterState {
  String selectedAnimalType;
  String selectedCattleId;
  String selectedDateRange;
  String searchQuery;

  FilterState({
    this.selectedAnimalType = 'All',
    this.selectedCattleId = 'All',
    this.selectedDateRange = 'All Time',
    this.searchQuery = '',
  });

  /// Check if any filters are active
  bool get hasActiveFilters {
    return selectedAnimalType != 'All' ||
           selectedCattleId != 'All' ||
           selectedDateRange != 'All Time' ||
           searchQuery.isNotEmpty;
  }

  /// Clear all filters
  void clearAll() {
    selectedAnimalType = 'All';
    selectedCattleId = 'All';
    selectedDateRange = 'All Time';
    searchQuery = '';
  }

  /// Copy with new values
  FilterState copyWith({
    String? selectedAnimalType,
    String? selectedCattleId,
    String? selectedDateRange,
    String? searchQuery,
  }) {
    return FilterState(
      selectedAnimalType: selectedAnimalType ?? this.selectedAnimalType,
      selectedCattleId: selectedCattleId ?? this.selectedCattleId,
      selectedDateRange: selectedDateRange ?? this.selectedDateRange,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

/// Data class to hold all the data needed for filtering
class FilterData {
  final Map<String, CattleIsar> cattleMap;
  final List<dynamic> animalTypes;
  final Map<String, String> animalTypeIdToName;
  final List<CattleIsar> filteredCattle;

  const FilterData({
    required this.cattleMap,
    required this.animalTypes,
    required this.animalTypeIdToName,
    required this.filteredCattle,
  });
}

/// Callback function types for filter events
typedef FilterStateCallback = void Function(FilterState filterState);
typedef SearchQueryCallback = void Function(String searchQuery);
typedef ClearFiltersCallback = void Function();

/// Default date range options
class DateRangeOptions {
  static const List<String> defaultOptions = [
    'All Time',
    'Today',
    '7 Days',
    '30 Days',
    '90 Days',
  ];

  static const List<String> extendedOptions = [
    'All Time',
    'Today',
    '7 Days',
    '30 Days',
    '90 Days',
    '6 Months',
    '1 Year',
  ];
}
