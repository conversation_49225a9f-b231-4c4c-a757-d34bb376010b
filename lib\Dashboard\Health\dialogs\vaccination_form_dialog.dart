import 'package:flutter/material.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import 'unified_health_form_dialog.dart';

/// Legacy wrapper for VaccinationFormDialog
/// Maintains backward compatibility while using the new unified form
class VaccinationFormDialog extends StatelessWidget {
  final String cattleId;
  final List<CattleIsar>? cattle;
  final VaccinationIsar? vaccination;
  final Future<bool> Function(VaccinationIsar)? onSave;

  const VaccinationFormDialog({
    Key? key,
    required this.cattleId,
    this.cattle,
    this.vaccination,
    this.onSave,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnifiedHealthFormDialog(
      formType: 'vaccination',
      cattle: cattle ?? [],
      initialCattleId: cattleId,
      existingRecord: vaccination,
      onSaved: () {
        // The unified dialog handles the save operation internally
        // This callback is called after successful save
      },
    );
  }
}
