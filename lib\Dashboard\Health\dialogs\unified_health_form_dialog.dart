import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../widgets/unified_health_form.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../../services/database/database_helper.dart';
import '../../../utils/message_utils.dart';

/// Unified dialog for creating/editing health, treatment, and vaccination records
class UnifiedHealthFormDialog extends StatefulWidget {
  final String formType; // 'health', 'treatment', 'vaccination'
  final List<CattleIsar> cattle;
  final String? initialCattleId;
  final dynamic existingRecord; // Can be HealthRecordIsar, TreatmentIsar, or VaccinationIsar
  final Function()? onSaved;

  const UnifiedHealthFormDialog({
    Key? key,
    required this.formType,
    required this.cattle,
    this.initialCattleId,
    this.existingRecord,
    this.onSaved,
  }) : super(key: key);

  @override
  State<UnifiedHealthFormDialog> createState() => _UnifiedHealthFormDialogState();
}

class _UnifiedHealthFormDialogState extends State<UnifiedHealthFormDialog> {
  static final Logger _logger = Logger('UnifiedHealthFormDialog');
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  
  bool _isSaving = false;
  Map<String, dynamic>? _initialData;

  @override
  void initState() {
    super.initState();
    _prepareInitialData();
  }

  void _prepareInitialData() {
    if (widget.existingRecord == null) return;

    try {
      switch (widget.formType) {
        case 'health':
          final record = widget.existingRecord as HealthRecordIsar;
          _initialData = {
            'cattleId': record.cattleId,
            'date': record.date ?? DateTime.now(),
            'condition': record.diagnosis ?? record.condition ?? '',
            'treatment': record.treatment ?? '',
            'medicine': record.medicine ?? '',
            'dose': record.dose ?? 0.0,
            'dosageUnit': record.dosageUnit ?? 'mg',
            'veterinarian': record.veterinarian ?? '',
            'cost': record.cost ?? 0.0,
            'notes': record.notes ?? '',
            'status': record.isResolved == true ? 'Completed' : 'Active',
          };
          break;
          
        case 'treatment':
          final record = widget.existingRecord as TreatmentIsar;
          _initialData = {
            'cattleId': record.cattleId,
            'date': record.date ?? DateTime.now(),
            'condition': record.condition ?? '',
            'treatment': record.treatment ?? '',
            'medicine': '', // TreatmentIsar doesn't have medicine field
            'dose': double.tryParse(record.dosage ?? '0') ?? 0.0, // Convert string dosage to double dose
            'dosageUnit': 'mg', // Default unit since TreatmentIsar doesn't have dosageUnit
            'veterinarian': record.veterinarian ?? '',
            'cost': double.tryParse(record.cost ?? '0') ?? 0.0,
            'notes': record.notes ?? '',
            'status': record.status ?? 'Active',
          };
          break;
          
        case 'vaccination':
          final record = widget.existingRecord as VaccinationIsar;
          _initialData = {
            'cattleId': record.cattleId,
            'date': record.date ?? DateTime.now(),
            'vaccineName': record.vaccineName ?? '',
            'manufacturer': record.manufacturer ?? '',
            'batchNumber': record.batchNumber ?? '',
            'cost': record.cost ?? 0.0,
            'notes': record.notes ?? '',
            'nextDueDate': record.nextDueDate,
          };
          break;
      }
    } catch (e) {
      _logger.severe('Error preparing initial data: $e');
    }
  }

  Future<void> _saveRecord(Map<String, dynamic> formData) async {
    if (_isSaving) return;
    
    setState(() => _isSaving = true);
    
    try {
      _logger.info('Saving ${widget.formType} record with data: $formData');
      
      switch (widget.formType) {
        case 'health':
          await _saveHealthRecord(formData);
          break;
        case 'treatment':
          await _saveTreatmentRecord(formData);
          break;
        case 'vaccination':
          await _saveVaccinationRecord(formData);
          break;
      }
      
      if (mounted) {
        Navigator.of(context).pop();
        widget.onSaved?.call();
        
        final recordType = widget.formType.substring(0, 1).toUpperCase() + 
                          widget.formType.substring(1);
        final action = widget.existingRecord == null ? 'created' : 'updated';
        
        HealthMessageUtils.showSuccess(
          context, 
          '$recordType record $action successfully'
        );
      }
    } catch (e) {
      _logger.severe('Error saving ${widget.formType} record: $e');
      if (mounted) {
        HealthMessageUtils.showError(
          context, 
          'Failed to save ${widget.formType} record: $e'
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  Future<void> _saveHealthRecord(Map<String, dynamic> data) async {
    if (widget.existingRecord != null) {
      // Update existing record - preserve the database ID
      final existing = widget.existingRecord as HealthRecordIsar;
      final updated = existing.copyWith(
        cattleId: data['cattleId'],
        date: data['date'],
        diagnosis: data['condition'],
        condition: data['condition'], // Also update condition field
        treatment: data['treatment'],
        medicine: data['medicine'],
        dose: data['dose'],
        dosageUnit: data['dosageUnit'],
        veterinarian: data['veterinarian'],
        cost: data['cost'],
        notes: data['notes'],
        isResolved: data['status'] == 'Completed',
      );
      await _dbHelper.healthHandler.addOrUpdateHealthRecord(updated);
    } else {
      // Create new record
      final record = HealthRecordIsar.create(
        cattleId: data['cattleId'],
        date: data['date'],
        diagnosis: data['condition'],
        treatment: data['treatment'],
        medicine: data['medicine'],
        dose: data['dose'],
        dosageUnit: data['dosageUnit'],
        veterinarian: data['veterinarian'],
        cost: data['cost'],
        notes: data['notes'],
        isResolved: data['status'] == 'Completed',
      );
      await _dbHelper.healthHandler.addOrUpdateHealthRecord(record);
    }
  }

  Future<void> _saveTreatmentRecord(Map<String, dynamic> data) async {
    // Convert unified form fields to TreatmentIsar format
    final dosageString = data['dose'] != null && data['dose'] > 0
        ? '${data['dose']} ${data['dosageUnit'] ?? 'mg'}'
        : '';

    if (widget.existingRecord != null) {
      // Update existing record - preserve the database ID
      final existing = widget.existingRecord as TreatmentIsar;
      final updated = TreatmentIsar(
        cattleId: data['cattleId'],
        date: data['date'],
        condition: data['condition'],
        treatment: data['treatment'],
        dosage: dosageString,
        veterinarian: data['veterinarian'],
        cost: data['cost']?.toString() ?? '0',
        notes: data['notes'],
        status: data['status'],
        businessId: existing.businessId, // Preserve existing businessId
        createdAt: existing.createdAt,
        updatedAt: DateTime.now(),
      )..id = existing.id; // Preserve the database ID

      await _dbHelper.healthHandler.addOrUpdateTreatment(data['cattleId'], updated);
    } else {
      // Create new record
      final record = TreatmentIsar(
        cattleId: data['cattleId'],
        date: data['date'],
        condition: data['condition'],
        treatment: data['treatment'],
        dosage: dosageString,
        veterinarian: data['veterinarian'],
        cost: data['cost']?.toString() ?? '0',
        notes: data['notes'],
        status: data['status'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await _dbHelper.healthHandler.addOrUpdateTreatment(data['cattleId'], record);
    }
  }

  Future<void> _saveVaccinationRecord(Map<String, dynamic> data) async {
    if (widget.existingRecord != null) {
      // Update existing record - preserve the database ID
      final existing = widget.existingRecord as VaccinationIsar;
      final updated = VaccinationIsar.create(
        recordId: existing.recordId,
        cattleId: data['cattleId'],
        date: data['date'],
        vaccineName: data['vaccineName'],
        manufacturer: data['manufacturer'],
        batchNumber: data['batchNumber'],
        cost: data['cost'],
        notes: data['notes'],
        nextDueDate: data['nextDueDate'],
      )..id = existing.id; // Preserve the database ID
      await _dbHelper.healthHandler.addOrUpdateVaccination(data['cattleId'], updated);
    } else {
      // Create new record
      final record = VaccinationIsar.create(
        cattleId: data['cattleId'],
        date: data['date'],
        vaccineName: data['vaccineName'],
        manufacturer: data['manufacturer'],
        batchNumber: data['batchNumber'],
        cost: data['cost'],
        notes: data['notes'],
        nextDueDate: data['nextDueDate'],
      );
      await _dbHelper.healthHandler.addOrUpdateVaccination(data['cattleId'], record);
    }
  }

  String _getDialogTitle() {
    final action = widget.existingRecord == null ? 'Add' : 'Edit';
    switch (widget.formType) {
      case 'health':
        return '$action Health Record';
      case 'treatment':
        return '$action Treatment Record';
      case 'vaccination':
        return '$action Vaccination Record';
      default:
        return '$action Record';
    }
  }

  IconData _getDialogIcon() {
    switch (widget.formType) {
      case 'health':
        return Icons.medical_services;
      case 'treatment':
        return Icons.healing;
      case 'vaccination':
        return Icons.vaccines;
      default:
        return Icons.health_and_safety;
    }
  }

  Color _getDialogColor() {
    switch (widget.formType) {
      case 'health':
        return Colors.blue;
      case 'treatment':
        return Colors.green;
      case 'vaccination':
        return Colors.teal;
      default:
        return Colors.blue;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: screenSize.width * 0.9,
          maxHeight: screenSize.height * 0.85,
          minWidth: 300,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getDialogColor().withAlpha(26),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: _getDialogColor().withAlpha(50),
                    child: Icon(
                      _getDialogIcon(),
                      color: _getDialogColor(),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _getDialogTitle(),
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Form Content
            Flexible(
              child: UnifiedHealthForm(
                formType: widget.formType,
                cattle: widget.cattle,
                initialCattleId: widget.initialCattleId,
                initialData: _initialData,
                onSave: _saveRecord,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
