import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../dialogs/milk_sale_entry_dialog.dart';
import '../models/milk_sale_isar.dart';
import '../services/milk_sales_service.dart';
import '../services/milk_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import 'package:logging/logging.dart';

class MilkSalesScreen extends StatefulWidget {
  const MilkSalesScreen({super.key});

  @override
  State<MilkSalesScreen> createState() => _MilkSalesScreenState();
}

class _MilkSalesScreenState extends State<MilkSalesScreen> {
  final _milkSalesService = MilkSalesService();
  final _milkService = MilkService();
  final _farmSetupHandler = FarmSetupHandler.instance;
  final _logger = Logger('MilkSalesScreen');
  DateTime _selectedDate = DateTime.now();
  List<MilkSaleIsar> _sales = [];
  List<MilkSaleIsar> _allSales = [];
  bool _isLoading = true;
  bool _showAllSales = true;

  // Cached summary calculations
  double _cachedTotalSales = 0.0;

  double _cachedAvgSalePerDay = 0.0;
  double _cachedTotalPaid = 0.0;
  List<MilkSaleIsar>? _lastCalculatedSales;

  // Currency settings
  String _currencySymbol = '\$';
  bool _symbolBeforeAmount = true;

  // Color scheme - Consistent with app theme, each used only once for headings/icons
  // Prohibited: orange, grey/gray, yellow, amber
  static const _primaryColor = Color(0xFF2E7D32); // App Theme Green - App bar, FAB, primary actions (consistent with app)
  static const _secondaryColor = Color(0xFF9C27B0); // Material Purple - Secondary elements
  static const _accentColor = Color(0xFF3F51B5); // Material Indigo - Accent elements
  static const _quaternaryColor = Color(0xFF009688); // Material Teal - Additional elements

  static const _backgroundBlue = Color(0xFFE3F2FD); // Light blue for backgrounds only
  static const _borderBlue = Color(0xFF90CAF9); // Medium blue for borders only

  @override
  void initState() {
    super.initState();
    _loadCurrencySettings();
    _fixExistingData();
    _refreshData();
  }

  // Fix any existing milk sales with empty saleId values
  Future<void> _fixExistingData() async {
    try {
      await _milkSalesService.fixEmptySaleIds();
    } catch (e) {
      _logger.warning('Error fixing existing data: $e');
      // Don't block the UI if this fails
    }
  }

  // Load currency settings from database
  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      _logger.warning('Error loading currency settings: $e');
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  // Format currency based on settings
  String _formatCurrency(double amount) {
    return _symbolBeforeAmount
        ? '$_currencySymbol${amount.toStringAsFixed(2)}'
        : '${amount.toStringAsFixed(2)}$_currencySymbol';
  }

  Future<void> _refreshData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Optimize: Load all sales once and filter locally
      final allSales = await _milkSalesService.getMilkSales();

      if (mounted) {
        setState(() {
          _allSales = allSales;
          _sales = _showAllSales ? allSales : _filterSalesByDate(allSales, _selectedDate);
          _isLoading = false;
          _lastCalculatedSales = null; // Reset cache when data changes
        });
      }
    } catch (e) {
      _logger.severe('Error loading sales data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  // Helper method to filter sales by date locally
  List<MilkSaleIsar> _filterSalesByDate(List<MilkSaleIsar> sales, DateTime date) {
    return sales.where((sale) =>
        sale.date.year == date.year &&
        sale.date.month == date.month &&
        sale.date.day == date.day).toList();
  }

  Future<void> _showEntryDialog() async {
    // Calculate available milk before showing the dialog
    final double availableMilk = await _calculateAvailableMilk();

    // Check if widget is still mounted before using the context
    if (!mounted) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => MilkSaleEntryDialog(
        selectedDate: _selectedDate,
        availableMilk: availableMilk,
      ),
    );

    if (result == true && mounted) {
      _refreshData();
    }
  }

  Future<double> _calculateAvailableMilk() async {
    try {
      final records = await _milkService.getMilkRecordsForDate(_selectedDate);
      final totalProduction = records.fold<double>(
          0.0,
          (sum, record) => sum +
              (record.morningAmount ?? 0) +
              (record.eveningAmount ?? 0));

      // Optimize: Use local data if available, otherwise fetch
      final salesForDate = _allSales.isNotEmpty
          ? _filterSalesByDate(_allSales, _selectedDate)
          : await _milkSalesService.getMilkSalesForDate(_selectedDate);

      final totalSold = salesForDate.fold<double>(0.0, (sum, sale) => sum + sale.quantity);

      return (totalProduction - totalSold).clamp(0.0, double.infinity);
    } catch (e) {
      _logger.warning('Error calculating available milk: $e');
      return 0.0;
    }
  }

  void _toggleViewMode() {
    setState(() {
      _showAllSales = !_showAllSales;
      _sales = _showAllSales ? _allSales : _filterSalesByDate(_allSales, _selectedDate);
      _lastCalculatedSales = null; // Reset cache when view changes
    });
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: _primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        if (!_showAllSales) {
          _sales = _filterSalesByDate(_allSales, _selectedDate);
          _lastCalculatedSales = null; // Reset cache when date changes
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Sales Records',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
        backgroundColor: _primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _toggleViewMode,
            tooltip: _showAllSales ? 'Show Today Only' : 'Show All Sales',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_month),
            onPressed: _selectDate,
            tooltip: 'Select Date',
          ),
        ],
      ),
      body: Container(
        color: Colors.white,
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: _primaryColor))
            : Column(
                children: [
                  // 1. Sales Summary header and cards
                  _buildSummaryCards(_sales),

                  // 3. Main context header
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 12.0),
                    color: _backgroundBlue,
                    child: Row(
                      children: [
                        Text(
                          _showAllSales
                              ? 'All Sales Records'
                              : 'Sales for ${DateFormat('dd MMM yyyy').format(_selectedDate)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: _primaryColor,
                          ),
                        ),
                        const Spacer(),
                        Chip(
                          label: Text(
                            '${_sales.length} Records',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          backgroundColor: _primaryColor,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                      ],
                    ),
                  ),

                  // 4. Sales data table
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            _buildListHeader(),
                            Expanded(
                              child: _sales.isEmpty
                                  ? Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.receipt_long,
                                            size: 64,
                                            color: _secondaryColor.withAlpha(150),
                                          ),
                                          const SizedBox(height: 16),
                                          Text(
                                            'No sales records found',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: _secondaryColor,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          ElevatedButton.icon(
                                            onPressed: _showEntryDialog,
                                            icon: const Icon(Icons.add),
                                            label: const Text('Add Sale'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: _primaryColor,
                                              foregroundColor: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : ListView.builder(
                                      itemCount: _sales.length,
                                      itemBuilder: (context, index) {
                                        final sale = _sales[index];
                                        return _buildSaleCard(sale);
                                      },
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: _primaryColor,
        onPressed: _showEntryDialog,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  // Optimized method to calculate summary with caching
  void _calculateSummary(List<MilkSaleIsar> sales) {
    if (_lastCalculatedSales == sales) return; // Use cached values

    _cachedTotalSales = sales.fold<double>(0.0, (sum, sale) => sum + sale.total);
    // Total quantity calculation removed as it was unused

    // Calculate average sale per day
    if (sales.isNotEmpty) {
      final uniqueDates = sales.map((sale) =>
        DateTime(sale.date.year, sale.date.month, sale.date.day)).toSet();
      _cachedAvgSalePerDay = uniqueDates.isNotEmpty ? (_cachedTotalSales / uniqueDates.length) : 0.0;
    } else {
      _cachedAvgSalePerDay = 0.0;
    }

    _cachedTotalPaid = sales
        .where((s) => s.paymentStatus == 'Paid')
        .fold<double>(0.0, (sum, sale) => sum + sale.total);

    _lastCalculatedSales = sales;
  }

  Widget _buildSummaryCards(List<MilkSaleIsar> sales) {
    _calculateSummary(sales);

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Summary section header - centered
          Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: Text(
              'Sales Summary',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          // Summary cards
          Row(
            children: [
              _buildSummaryCard(
                title: 'Total Sales',
                value: _formatCurrency(_cachedTotalSales),
                icon: Icons.attach_money,
                color: _secondaryColor, // Blue - same for label and value
              ),
              const SizedBox(width: 16),
              _buildSummaryCard(
                title: 'Avg Sale per Day',
                value: _formatCurrency(_cachedAvgSalePerDay),
                icon: Icons.trending_up,
                color: _accentColor, // Pink - same for label and value
              ),
              const SizedBox(width: 16),
              _buildSummaryCard(
                title: 'Paid Amount',
                value: _formatCurrency(_cachedTotalPaid),
                icon: Icons.verified_user,
                color: _quaternaryColor, // Cyan - same for label and value
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
      {required String title,
      required String value,
      required IconData icon,
      required Color color}) {

    return Expanded(
      child: Container(
        height: 100, // Increased height
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: _backgroundBlue,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: _borderBlue.withAlpha(26),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 18),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 13,
                      color: color, // Same color as icon and value
                      fontWeight: FontWeight.w500,
                    ),
                    softWrap: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Expanded(
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 18,
                      color: color, // Same color as label and icon
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: _primaryColor.withAlpha(25),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Row(
        children: [
          // Even spacing - each column gets equal flex
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Date',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor, // Green
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'DD/MM/YY',
                  style: TextStyle(
                    fontSize: 14, // Match label size
                    color: _primaryColor, // Green - Same color as label
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Quantity',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _secondaryColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Liters',
                  style: TextStyle(
                    fontSize: 14, // Match label size
                    color: _secondaryColor, // Same color as label
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Amount',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _accentColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _currencySymbol,
                  style: TextStyle(
                    fontSize: 14, // Match label size
                    color: _accentColor, // Same color as label
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Status',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _quaternaryColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Payment',
                  style: TextStyle(
                    fontSize: 14, // Match label size
                    color: _quaternaryColor, // Same color as label
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaleCard(MilkSaleIsar sale) {
    // Get the formatted short date
    final dateStr = DateFormat('dd/MM/yy').format(sale.date);

    // Format payment status
    final isPaid = sale.paymentStatus == 'Paid';

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: _borderBlue.withAlpha(50),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Date column - even spacing
              Expanded(
                flex: 1,
                child: Text(
                  dateStr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: _primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              // Quantity column - even spacing
              Expanded(
                flex: 1,
                child: Text(
                  sale.quantity.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: _secondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              // Amount column - even spacing
              Expanded(
                flex: 1,
                child: Text(
                  _formatCurrency(sale.total),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _accentColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              // Status column - even spacing
              Expanded(
                flex: 1,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: isPaid ? Colors.green.shade100 : _backgroundBlue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      isPaid ? 'Paid' : 'Pending',
                      style: TextStyle(
                        color: _quaternaryColor, // Always use quaternary color to match header
                        fontWeight: FontWeight.bold,
                        fontSize: 11,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Notes section (if any)
          if (sale.notes != null && sale.notes!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.note, size: 14, color: _primaryColor),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    sale.notes!,
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                      color: _primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }


}
