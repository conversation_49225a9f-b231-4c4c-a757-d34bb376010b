import 'dart:async';
import 'package:logging/logging.dart';
import '../../../services/database/database_helper.dart';
import '../../Notifications/models/notification_isar.dart';
import '../../Notifications/services/notifications_handler.dart';
import '../models/health_record_isar.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';

/// Service for generating and managing health-related alerts
class HealthAlertService {
  static final Logger _logger = Logger('HealthAlertService');
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  late final NotificationsHandler _notificationsHandler;
  late final FarmSetupHandler _farmSetupHandler;

  HealthAlertService() {
    _notificationsHandler = _dbHelper.notificationsHandler;
    _farmSetupHandler = _dbHelper.farmSetupHandler;
  }

  /// Generate all types of health alerts
  Future<void> generateAlerts() async {
    try {
      _logger.info('Starting health alert generation');
      
      // Check if health alerts are enabled
      final alertSettings = await _farmSetupHandler.getAlertSettings();
      if (!alertSettings.vaccinationAlerts && !alertSettings.healthCheckAlerts) {
        _logger.info('Health alerts are disabled');
        return;
      }
      
      // Generate different types of alerts
      await Future.wait([
        if (alertSettings.vaccinationAlerts) _generateVaccinationAlerts(),
        if (alertSettings.healthCheckAlerts) _generateFollowUpAlerts(),
        _generateOverdueHealthCheckAlerts(),
      ]);
      
      _logger.info('Health alert generation completed');
    } catch (e) {
      _logger.severe('Error generating health alerts: $e');
    }
  }

  /// Generate vaccination due and overdue alerts
  Future<void> _generateVaccinationAlerts() async {
    try {
      _logger.info('Generating vaccination alerts');
      
      final now = DateTime.now();
      final allVaccinations = await _dbHelper.healthHandler.getAllVaccinations();
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      
      // Create a map for quick cattle lookup
      final cattleMap = <String, String>{};
      for (var cattle in allCattle) {
        if (cattle.businessId != null && cattle.name != null) {
          cattleMap[cattle.businessId!] = cattle.name!;
        }
      }

      for (var vaccination in allVaccinations) {
        if (vaccination.nextDueDate != null && vaccination.cattleId != null) {
          final dueDate = vaccination.nextDueDate!;
          final daysDifference = dueDate.difference(now).inDays;
          final cattleName = cattleMap[vaccination.cattleId!] ?? 'Unknown';

          // Vaccination overdue
          if (daysDifference < 0) {
            await _createAlert(
              type: 'vaccination',
              title: 'Vaccination Overdue',
              message: 'Vaccination for $cattleName is overdue by ${(-daysDifference)} days. '
                      'Vaccine: ${vaccination.vaccineName ?? 'Unknown'}',
              priority: 'high',
              cattleId: vaccination.cattleId,
              recordId: vaccination.recordId,
            );
          }
          // Vaccination due within 7 days
          else if (daysDifference <= 7) {
            await _createAlert(
              type: 'vaccination',
              title: 'Vaccination Due Soon',
              message: 'Vaccination for $cattleName is due in $daysDifference days. '
                      'Vaccine: ${vaccination.vaccineName ?? 'Unknown'}',
              priority: 'medium',
              cattleId: vaccination.cattleId,
              recordId: vaccination.recordId,
            );
          }
        }
      }
    } catch (e) {
      _logger.severe('Error generating vaccination alerts: $e');
    }
  }

  /// Generate follow-up appointment alerts
  Future<void> _generateFollowUpAlerts() async {
    try {
      _logger.info('Generating follow-up alerts');
      
      final now = DateTime.now();
      final allHealthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      
      // Create a map for quick cattle lookup
      final cattleMap = <String, String>{};
      for (var cattle in allCattle) {
        if (cattle.businessId != null && cattle.name != null) {
          cattleMap[cattle.businessId!] = cattle.name!;
        }
      }

      for (var record in allHealthRecords) {
        if (record.followUpRequired == true && 
            record.followUpDate != null && 
            record.cattleId != null) {
          
          final followUpDate = record.followUpDate!;
          final daysDifference = followUpDate.difference(now).inDays;
          final cattleName = cattleMap[record.cattleId!] ?? 'Unknown';

          // Follow-up overdue
          if (daysDifference < 0) {
            await _createAlert(
              type: 'health',
              title: 'Follow-up Overdue',
              message: 'Follow-up appointment for $cattleName is overdue by ${(-daysDifference)} days. '
                      'Condition: ${record.condition ?? 'Unknown'}',
              priority: 'high',
              cattleId: record.cattleId,
              recordId: record.recordId,
            );
          }
          // Follow-up due within 3 days
          else if (daysDifference <= 3) {
            await _createAlert(
              type: 'health',
              title: 'Follow-up Due Soon',
              message: 'Follow-up appointment for $cattleName is due in $daysDifference days. '
                      'Condition: ${record.condition ?? 'Unknown'}',
              priority: 'medium',
              cattleId: record.cattleId,
              recordId: record.recordId,
            );
          }
        }
      }
    } catch (e) {
      _logger.severe('Error generating follow-up alerts: $e');
    }
  }

  /// Generate alerts for cattle that haven't had health checks in a while
  Future<void> _generateOverdueHealthCheckAlerts() async {
    try {
      _logger.info('Generating overdue health check alerts');
      
      final now = DateTime.now();
      final sixMonthsAgo = DateTime(now.year, now.month - 6, now.day);
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      final allHealthRecords = await _dbHelper.healthHandler.getAllHealthRecords();

      // Group health records by cattle
      final cattleHealthRecords = <String, List<HealthRecordIsar>>{};
      for (var record in allHealthRecords) {
        if (record.cattleId != null) {
          cattleHealthRecords.putIfAbsent(record.cattleId!, () => []).add(record);
        }
      }

      for (var cattle in allCattle) {
        if (cattle.businessId == null) continue;

        final healthRecords = cattleHealthRecords[cattle.businessId!] ?? [];
        
        if (healthRecords.isEmpty) {
          // No health records at all
          await _createAlert(
            type: 'health',
            title: 'No Health Records',
            message: 'No health records found for ${cattle.name ?? 'Unknown'}. '
                    'Consider scheduling a health check.',
            priority: 'low',
            cattleId: cattle.businessId,
          );
        } else {
          // Check if last health record is older than 6 months
          healthRecords.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
          final lastRecord = healthRecords.first;
          
          if (lastRecord.date != null && lastRecord.date!.isBefore(sixMonthsAgo)) {
            final monthsSinceLastCheck = now.difference(lastRecord.date!).inDays ~/ 30;
            
            await _createAlert(
              type: 'health',
              title: 'Health Check Overdue',
              message: 'Last health check for ${cattle.name ?? 'Unknown'} was $monthsSinceLastCheck months ago. '
                      'Consider scheduling a health check.',
              priority: monthsSinceLastCheck > 12 ? 'high' : 'medium',
              cattleId: cattle.businessId,
            );
          }
        }
      }
    } catch (e) {
      _logger.severe('Error generating overdue health check alerts: $e');
    }
  }

  /// Create a health alert notification
  Future<void> _createAlert({
    required String type,
    required String title,
    required String message,
    required String priority,
    String? cattleId,
    String? recordId,
  }) async {
    try {
      // Check if similar alert already exists (avoid duplicates)
      final existingAlerts = await _notificationsHandler.getNotificationsByType(type);
      final duplicateExists = existingAlerts.any((alert) => 
        alert.cattleId == cattleId && 
        alert.title == title &&
        alert.isRead == false
      );

      if (duplicateExists) {
        _logger.info('Skipping duplicate alert for cattle $cattleId');
        return;
      }

      final notification = NotificationIsar(
        businessId: 'health_alert_${DateTime.now().millisecondsSinceEpoch}_${cattleId ?? 'system'}',
        type: type,
        title: title,
        message: message,
        priority: priority,
        cattleId: cattleId,
        recordId: recordId,
        createdAt: DateTime.now(),
        isRead: false,
      );
      
      await _notificationsHandler.addNotification(notification);
      _logger.info('Created $priority priority $type alert for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error creating health alert: $e');
    }
  }

  /// Get count of unread health alerts
  Future<int> getUnreadHealthAlertsCount() async {
    try {
      final healthAlerts = await _notificationsHandler.getNotificationsByType('health');
      final vaccinationAlerts = await _notificationsHandler.getNotificationsByType('vaccination');
      
      final unreadCount = healthAlerts.where((alert) => !alert.isRead).length +
                         vaccinationAlerts.where((alert) => !alert.isRead).length;
      
      return unreadCount;
    } catch (e) {
      _logger.severe('Error getting unread health alerts count: $e');
      return 0;
    }
  }

  /// Mark health alert as read
  Future<void> markAlertAsRead(String alertId) async {
    try {
      await _notificationsHandler.markNotificationAsRead(alertId);
      _logger.info('Marked health alert $alertId as read');
    } catch (e) {
      _logger.severe('Error marking health alert as read: $e');
    }
  }

  /// Get all health alerts (read and unread)
  Future<List<NotificationIsar>> getAllHealthAlerts() async {
    try {
      final healthAlerts = await _notificationsHandler.getNotificationsByType('health');
      final vaccinationAlerts = await _notificationsHandler.getNotificationsByType('vaccination');
      
      final allAlerts = [...healthAlerts, ...vaccinationAlerts];
      
      // Sort by priority and creation date
      allAlerts.sort((a, b) {
        // High priority first
        final priorityOrder = {'high': 0, 'medium': 1, 'low': 2};
        final aPriority = priorityOrder[a.priority] ?? 3;
        final bPriority = priorityOrder[b.priority] ?? 3;
        
        if (aPriority != bPriority) {
          return aPriority.compareTo(bPriority);
        }
        
        // Then by creation date (newest first)
        return (b.createdAt ?? DateTime(0)).compareTo(a.createdAt ?? DateTime(0));
      });
      
      return allAlerts;
    } catch (e) {
      _logger.severe('Error getting all health alerts: $e');
      return [];
    }
  }

  /// Delete old resolved alerts (older than 30 days)
  Future<void> cleanupOldAlerts() async {
    try {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final allAlerts = await getAllHealthAlerts();
      
      for (var alert in allAlerts) {
        if (alert.isRead && 
            alert.createdAt != null && 
            alert.createdAt!.isBefore(thirtyDaysAgo)) {
          await _notificationsHandler.deleteNotification(alert.businessId ?? '');
        }
      }
      
      _logger.info('Cleaned up old health alerts');
    } catch (e) {
      _logger.severe('Error cleaning up old health alerts: $e');
    }
  }
}
