import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import 'unified_health_form_dialog.dart';

/// Legacy wrapper for HealthRecordFormDialog
/// Maintains backward compatibility while using the new unified form
class HealthRecordFormDialog extends StatelessWidget {
  final HealthRecordIsar? healthRecord;
  final List<CattleIsar> cattle;
  final Future<void> Function(HealthRecordIsar)? onSave;

  const HealthRecordFormDialog({
    Key? key,
    this.healthRecord,
    required this.cattle,
    this.onSave,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnifiedHealthFormDialog(
      formType: 'health',
      cattle: cattle,
      existingRecord: healthRecord,
      onSaved: () {
        // The unified dialog handles the save operation internally
        // This callback is called after successful save
      },
    );
  }
}
