import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../Farm Setup/services/farm_setup_handler.dart';
import '../Help/screens/help_screen.dart';
import '../Farm Setup/models/farm_isar.dart';
import '../Profile/screens/profile_screen.dart';
import '../Settings/screens/settings_screen.dart';
import '../Farm Setup/screens/farm_info_screen.dart';
import '../Reports/screens/reports_screen.dart';
import '../Notifications/screens/notifications_screen.dart';
import '../Settings/services/auth_service.dart';
import '../../utils/message_utils.dart';

// Constants for strings and padding values
class _AppStrings {
  static const String selectFarm = 'Select Farm';
  static const String manageYourFarm = 'Manage Your Farm';
  static const String dashboard = 'Dashboard';
  static const String farmSetup = 'Farm Setup';
  static const String reportsAnalytics = 'Reports & Analytics';
  static const String notifications = 'Notifications';
  static const String helpSupport = 'Help & Support';
  static const String account = 'Account';
  static const String profile = 'Profile';
  static const String settings = 'Settings';
  static const String logout = 'Logout';
  static const String version = 'Version 1.0.0';
  static const String errorLoadingFarms = 'Error loading farms: ';
}

class _AppPadding {
  static const double small = 8.0;
  static const double medium = 16.0;
}

class FarmSelectionDrawer extends StatefulWidget {
  const FarmSelectionDrawer({Key? key}) : super(key: key);

  @override
  State<FarmSelectionDrawer> createState() => _FarmSelectionDrawerState();
}

class _FarmSelectionDrawerState extends State<FarmSelectionDrawer> {
  final FarmSetupHandler _farmSetupHandler = GetIt.instance<FarmSetupHandler>();
  FarmIsar? _selectedFarm;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFarms();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _farmSetupHandler.removeListener(_onFarmChanged);
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _farmSetupHandler.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadFarms();
  }

  Future<void> _loadFarms() async {
    try {
      // Use Future.wait for potential parallelism
      final results = await Future.wait([
        _farmSetupHandler.getAllFarms(),
        _farmSetupHandler.getSelectedFarmId(),
      ]);

      final farms = results[0] as List<FarmIsar>;
      final selectedFarmId = results[1] as String?;

      if (!mounted) return;

      FarmIsar? selectedFarm;
      if (farms.isNotEmpty) {
        if (selectedFarmId != null) {
          try {
            selectedFarm = farms.firstWhere(
              (farm) => farm.id.toString() == selectedFarmId,
            );
          } catch (e) {
            // Handle case where selectedFarmId doesn't match any current farm
            debugPrint(
                'Selected farm ID $selectedFarmId not found. Defaulting to first.');
            selectedFarm = farms.first;
          }
        } else {
          // If no ID stored, default to the first farm
          selectedFarm = farms.first;
        }
      } else {
        selectedFarm = null;
      }

      setState(() {
        _selectedFarm = selectedFarm;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      debugPrint('${_AppStrings.errorLoadingFarms}$e\n$stackTrace');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Helper for navigation to reduce repetition
  void _navigateTo(Widget screen) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    bool showDivider = false,
  }) {
    final theme = Theme.of(context);

    return Column(
      children: [
        ListTile(
          leading: Icon(icon, color: iconColor ?? theme.colorScheme.primary),
          title: Text(
            title,
            style: theme.textTheme.titleMedium,
          ),
          onTap: onTap,
        ),
        if (showDivider) Divider(height: 1, color: theme.dividerColor),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final mediaQuery = MediaQuery.of(context);

    if (_isLoading) {
      return Drawer(
        child: Center(
            child: CircularProgressIndicator(color: theme.colorScheme.primary)),
      );
    }

    return Drawer(
      child: Container(
        color: colorScheme.surface,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(
                top: mediaQuery.padding.top + _AppPadding.medium,
                bottom: _AppPadding.medium,
                left: _AppPadding.medium,
                right: _AppPadding.medium,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: colorScheme.secondaryContainer,
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: colorScheme.onSecondaryContainer,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _selectedFarm?.name ?? _AppStrings.selectFarm,
                    style: textTheme.headlineSmall?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _AppStrings.manageYourFarm,
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withAlpha(179),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  const SizedBox(height: _AppPadding.small),
                  Divider(height: 1, color: theme.dividerColor),
                  _buildDrawerItem(
                    icon: Icons.dashboard,
                    title: _AppStrings.dashboard,
                    onTap: () => Navigator.pop(context),
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings_applications,
                    title: _AppStrings.farmSetup,
                    onTap: () => _navigateTo(const FarmInfoScreen()),
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.analytics,
                    title: _AppStrings.reportsAnalytics,
                    onTap: () => _navigateTo(const ReportsScreen()),
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.notifications,
                    title: _AppStrings.notifications,
                    onTap: () => _navigateTo(const NotificationsScreen()),
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.help_outline,
                    title: _AppStrings.helpSupport,
                    onTap: () => _navigateTo(const HelpScreen()),
                    showDivider: true,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(_AppPadding.medium),
                    child: Text(
                      _AppStrings.account,
                      style: textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  _buildDrawerItem(
                    icon: Icons.person,
                    title: _AppStrings.profile,
                    onTap: () => _navigateTo(const ProfileScreen()),
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings,
                    title: _AppStrings.settings,
                    onTap: () => _navigateTo(const SettingsScreen()),
                  ),
                  _buildDrawerItem(
                    icon: Icons.logout,
                    title: _AppStrings.logout,
                    onTap: () async {
                      // Close the drawer first
                      Navigator.pop(context);

                      // Show a confirmation dialog
                      final shouldLogout = await showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Logout'),
                              content: const Text(
                                  'Are you sure you want to logout?'),
                              actions: [
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(false),
                                  child: const Text('Cancel'),
                                ),
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(true),
                                  child: const Text('Logout'),
                                ),
                              ],
                            ),
                          ) ??
                          false;

                      if (shouldLogout && context.mounted) {
                        try {
                          // Show loading indicator
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (context) => const Center(
                              child: CircularProgressIndicator(),
                            ),
                          );

                          // Get the auth service and sign out
                          final authService = GetIt.instance<AuthService>();
                          await authService.signOut();

                          // Close loading indicator
                          if (context.mounted) {
                            Navigator.of(context).pop();

                            // Show success message
                            MessageUtils.showSuccess(context, 'Logged out successfully');

                            // Navigate back to dashboard (optional, as we're already there)
                            // Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
                          }
                        } catch (e) {
                          // Handle errors
                          if (context.mounted) {
                            Navigator.of(context)
                                .pop(); // Close loading indicator
                            MessageUtils.showError(context, 'Error during logout: $e');
                          }
                        }
                      }
                    },
                    iconColor: colorScheme.error,
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(_AppPadding.medium),
              child: Text(
                _AppStrings.version,
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
