import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Notifications/models/notification_isar.dart';
import '../services/health_alert_service.dart';
import '../../../utils/message_utils.dart';
import '../../../services/database/database_helper.dart';

/// Widget to display health alerts in the health tab
class HealthAlertsWidget extends StatefulWidget {
  final String? cattleId;
  final bool showAllAlerts;
  final int maxAlertsToShow;

  const HealthAlertsWidget({
    Key? key,
    this.cattleId,
    this.showAllAlerts = false,
    this.maxAlertsToShow = 3,
  }) : super(key: key);

  @override
  State<HealthAlertsWidget> createState() => _HealthAlertsWidgetState();
}

class _HealthAlertsWidgetState extends State<HealthAlertsWidget> {
  final HealthAlertService _alertService = HealthAlertService();
  List<NotificationIsar> _alerts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAlerts();
  }

  Future<void> _loadAlerts() async {
    try {
      setState(() => _isLoading = true);
      
      final allAlerts = await _alertService.getAllHealthAlerts();
      
      // Filter alerts by cattle if specified
      List<NotificationIsar> filteredAlerts;
      if (widget.cattleId != null && !widget.showAllAlerts) {
        filteredAlerts = allAlerts.where((alert) => alert.cattleId == widget.cattleId).toList();
      } else {
        filteredAlerts = allAlerts;
      }

      // Limit number of alerts if not showing all
      if (!widget.showAllAlerts && filteredAlerts.length > widget.maxAlertsToShow) {
        filteredAlerts = filteredAlerts.take(widget.maxAlertsToShow).toList();
      }

      if (mounted) {
        setState(() {
          _alerts = filteredAlerts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        MessageUtils.showError(context, 'Error loading health alerts: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (_alerts.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 12),
                  Text(
                    widget.cattleId != null ? 'No Health Alerts' : 'All Health Alerts Clear',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                widget.cattleId != null 
                    ? 'This cattle has no pending health alerts.'
                    : 'All cattle health records are up to date!',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: _getHighestPriorityColor(),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.cattleId != null ? 'Health Alerts' : 'Health Alerts (${_alerts.length})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (!widget.showAllAlerts && _alerts.length >= widget.maxAlertsToShow)
                  TextButton(
                    onPressed: () => _showAllAlertsDialog(),
                    child: const Text('View All'),
                  ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _alerts.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              return _buildAlertTile(_alerts[index]);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAlertTile(NotificationIsar alert) {
    final priorityColor = _getPriorityColor(alert.priority);
    final priorityIcon = _getPriorityIcon(alert.priority);

    return ListTile(
      leading: Icon(priorityIcon, color: priorityColor, size: 20),
      title: Text(
        alert.title ?? 'Health Alert',
        style: TextStyle(
          fontWeight: alert.isRead ? FontWeight.normal : FontWeight.bold,
          fontSize: 14,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            alert.message ?? '',
            style: const TextStyle(fontSize: 12),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            alert.createdAt != null 
                ? DateFormat('MMM dd, yyyy HH:mm').format(alert.createdAt!)
                : 'Unknown date',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!alert.isRead)
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: priorityColor,
                shape: BoxShape.circle,
              ),
            ),
          const SizedBox(width: 8),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, size: 16),
            onSelected: (value) => _handleAlertAction(value, alert),
            itemBuilder: (context) => [
              if (!alert.isRead)
                const PopupMenuItem(
                  value: 'mark_read',
                  child: Text('Mark as Read'),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Text('Delete'),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _showAlertDetails(alert),
      dense: true,
    );
  }

  Color _getPriorityColor(String? priority) {
    switch (priority?.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getPriorityIcon(String? priority) {
    switch (priority?.toLowerCase()) {
      case 'high':
        return Icons.error;
      case 'medium':
        return Icons.warning;
      case 'low':
        return Icons.info;
      default:
        return Icons.notifications;
    }
  }

  Color _getHighestPriorityColor() {
    if (_alerts.any((alert) => alert.priority == 'high')) {
      return Colors.red;
    } else if (_alerts.any((alert) => alert.priority == 'medium')) {
      return Colors.orange;
    } else {
      return Colors.blue;
    }
  }

  Future<void> _handleAlertAction(String action, NotificationIsar alert) async {
    try {
      switch (action) {
        case 'mark_read':
          await _alertService.markAlertAsRead(alert.businessId ?? '');
          if (mounted) {
            MessageUtils.showSuccess(context, 'Alert marked as read');
            _loadAlerts();
          }
          break;
        case 'delete':
          final confirmed = await _showDeleteConfirmation();
          if (confirmed == true) {
            // Use DatabaseHelper to access NotificationsHandler
            final dbHelper = DatabaseHelper.instance;
            await dbHelper.notificationsHandler.deleteNotification(alert.businessId ?? '');
            if (mounted) {
              MessageUtils.showSuccess(context, 'Alert deleted');
              _loadAlerts();
            }
          }
          break;
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error: $e');
      }
    }
  }

  Future<bool?> _showDeleteConfirmation() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Alert'),
        content: const Text('Are you sure you want to delete this alert?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showAlertDetails(NotificationIsar alert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(alert.title ?? 'Health Alert'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(alert.message ?? ''),
              const SizedBox(height: 16),
              if (alert.createdAt != null) ...[
                const Text(
                  'Created:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(DateFormat('MMMM dd, yyyy HH:mm').format(alert.createdAt!)),
                const SizedBox(height: 8),
              ],
              const Text(
                'Priority:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  Icon(
                    _getPriorityIcon(alert.priority),
                    color: _getPriorityColor(alert.priority),
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    alert.priority?.toUpperCase() ?? 'UNKNOWN',
                    style: TextStyle(
                      color: _getPriorityColor(alert.priority),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (!alert.isRead)
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _handleAlertAction('mark_read', alert);
              },
              child: const Text('Mark as Read'),
            ),
        ],
      ),
    );
  }

  void _showAllAlertsDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.7,
          child: Column(
            children: [
              AppBar(
                title: const Text('All Health Alerts'),
                automaticallyImplyLeading: false,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              Expanded(
                child: HealthAlertsWidget(
                  cattleId: widget.cattleId,
                  showAllAlerts: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
