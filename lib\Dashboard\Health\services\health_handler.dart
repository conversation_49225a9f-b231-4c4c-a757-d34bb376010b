import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';

import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../services/streams/stream_service.dart';

/// Consolidated handler for all Health module database operations
class HealthHandler {
  static final Logger _logger = Logger('HealthHandler');
  final IsarService _isarService;

  // Singleton instance
  static final HealthHandler _instance = HealthHandler._internal();
  static HealthHandler get instance => _instance;

  // Private constructor
  HealthHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== HEALTH RECORDS ===//

  /// Retrieves health records for a specific cattle
  Future<List<HealthRecordIsar>> getHealthRecordsForCattle(
      String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.healthRecordIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving health records for cattle $cattleId', e);
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve health records', e.toString());
    }
  }

  /// Adds a new health record for a cattle
  Future<void> addHealthRecord(HealthRecordIsar record) async {
    try {
      if (record.cattleId?.isEmpty ?? true) {
        throw ValidationException('Cattle ID is required');
      }

      await _isar.writeTxn(() async {
        await _isar.healthRecordIsars.put(record);
      });
    } catch (e) {
      _logger.severe(
          'Error adding health record for cattle ${record.cattleId}', e);
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add health record', e.toString());
    }
  }

  /// Retrieves all health records across all cattle
  Future<List<HealthRecordIsar>> getAllHealthRecords() async {
    try {
      return await _isar.healthRecordIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all health records', e);
      throw DatabaseException(
          'Failed to retrieve all health records', e.toString());
    }
  }

  /// Adds or updates a health record
  /// Returns the saved record with its ID
  Future<HealthRecordIsar> addOrUpdateHealthRecord(HealthRecordIsar record) async {
    try {
      _logger.info('🔵 Starting addOrUpdateHealthRecord');
      _logger.info('🔵 Input record data: cattleId=${record.cattleId}, date=${record.date}, recordType=${record.recordType}, treatment=${record.treatment}, diagnosis=${record.diagnosis}');

      // Validate the health record
      _logger.info('🔵 Validating health record...');
      _validateHealthRecord(record);
      _logger.info('🔵 Health record validation passed');

      if (record.recordId?.isEmpty ?? true) {
        // New record without ID - use generateRecordId with required params
        final generatedId = HealthRecordIsar.generateRecordId(
            record.cattleId ?? '',
            record.date ?? DateTime.now(),
            record.recordType ?? 'general');
        record.recordId = generatedId;
        _logger.info('🔵 Generated new record ID: $generatedId');
      } else {
        _logger.info('🔵 Using existing record ID: ${record.recordId}');
      }

      late int recordId;
      _logger.info('🔵 Starting database transaction...');
      // Add/update the record
      await _isar.writeTxn(() async {
        recordId = await _isar.healthRecordIsars.put(record);
        _logger.info('🔵 Record saved with database ID: $recordId');
      });

      _logger.info('🔵 Fetching saved record...');
      // Fetch the saved record to ensure we have the complete object with ID
      final savedRecord = await _isar.healthRecordIsars.get(recordId);
      if (savedRecord == null) {
        _logger.severe('🔴 Failed to retrieve saved record with ID: $recordId');
        throw DatabaseException('Failed to retrieve saved record', 'Record not found after saving');
      }

      _logger.info('🟢 Successfully added/updated health record: ${savedRecord.recordId}');
      _logger.info('🟢 Saved record details: cattleId=${savedRecord.cattleId}, date=${savedRecord.date}, recordType=${savedRecord.recordType}');

      // Notify stream listeners about the health record change
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyHealthChange({
        'action': record.recordId?.isEmpty ?? true ? 'add' : 'update',
        'cattleId': savedRecord.cattleId,
        'recordId': savedRecord.recordId,
        'record': savedRecord.toMap(),
      });

      return savedRecord;
    } catch (e, stackTrace) {
      _logger.severe('🔴 Error adding/updating health record: $e');
      _logger.severe('🔴 Stack trace: $stackTrace');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to add/update health record', e.toString());
    }
  }

  /// Adds a new treatment record
  /// Returns the saved treatment with its ID
  Future<MedicationIsar> addTreatment(MedicationIsar treatment) async {
    try {
      // Validate the treatment
      if (treatment.name?.isEmpty ?? true) {
        throw ValidationException('Treatment name is required');
      }
      if (treatment.cattleId?.isEmpty ?? true) {
        throw ValidationException('Cattle ID is required');
      }
      if (treatment.dosage?.isEmpty ?? true) {
        throw ValidationException('Dosage is required');
      }

      // Generate ID if needed
      if (treatment.businessId?.isEmpty ?? true) {
        treatment.businessId = MedicationIsar.generateBusinessId(
            treatment.cattleBusinessId ?? '',
            treatment.startDate ?? DateTime.now(),
            treatment.name ?? '');
      }

      late int recordId;
      // Add the treatment
      await _isar.writeTxn(() async {
        recordId = await _isar.medicationIsars.put(treatment);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedTreatment = await _isar.medicationIsars.get(recordId);
      if (savedTreatment == null) {
        throw DatabaseException('Failed to retrieve saved treatment', 'Treatment not found after saving');
      }

      _logger.info('Successfully added treatment: ${savedTreatment.businessId}');

      return savedTreatment;
    } catch (e) {
      _logger.severe('Error adding treatment', e);
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add treatment', e.toString());
    }
  }

  /// Deletes a health record by ID
  Future<void> deleteHealthRecord(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Record ID is required');
      }

      // Check if the record exists
      final record = await _isar.healthRecordIsars
          .filter()
          .recordIdEqualTo(recordId)
          .findFirst();

      if (record == null) {
        throw RecordNotFoundException(
            'Health record not found with ID: $recordId');
      }

      // Store cattle ID before deletion for notification
      final cattleId = record.cattleId;

      // Delete the record
      await _isar.writeTxn(() async {
        await _isar.healthRecordIsars.delete(record.id);
      });

      _logger.info('Successfully deleted health record: $recordId');

      // Notify stream listeners about the health record deletion
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyHealthChange({
        'action': 'delete',
        'cattleId': cattleId,
        'recordId': recordId,
      });
    } catch (e) {
      _logger.severe('Error deleting health record: $recordId', e);
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete health record', e.toString());
    }
  }

  /// Validates a health record
  void _validateHealthRecord(HealthRecordIsar record) {
    _logger.info('🔵 Validating health record fields...');

    if (record.cattleId?.isEmpty ?? true) {
      _logger.severe('🔴 Validation failed: Cattle ID is empty or null');
      throw ValidationException('Cattle ID is required');
    }
    _logger.info('🔵 Cattle ID validation passed: ${record.cattleId}');

    if (record.date == null) {
      _logger.severe('🔴 Validation failed: Record date is null');
      throw ValidationException('Record date is required');
    }
    _logger.info('🔵 Date validation passed: ${record.date}');

    if (record.recordType?.isEmpty ?? true) {
      _logger.severe('🔴 Validation failed: Record type is empty or null');
      throw ValidationException('Record type is required');
    }
    _logger.info('🔵 Record type validation passed: ${record.recordType}');

    _logger.info('🟢 All health record validations passed');
  }

  //=== MEDICATION RECORDS ===//

  /// Retrieves medications for a specific cattle
  Future<List<MedicationIsar>> getMedicationsForCattle(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.medicationIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving medications for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException('Failed to retrieve medications', e.toString());
    }
  }

  /// Retrieves all medication records across all cattle
  Future<List<MedicationIsar>> getAllMedications() async {
    try {
      return await _isar.medicationIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all medications', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to retrieve all medications', e.toString());
    }
  }

  /// Adds or updates a medication record for a specific cattle
  Future<void> addOrUpdateMedication(
      String cattleId, MedicationIsar record) async {
    try {
      // Validate required fields
      _validateMedicationRecord(record);

      // Ensure cattleId is set on the record
      record.cattleId = cattleId;

      // Use auto-increment ID
      // No need to set record.id manually as Isar will auto-increment

      await _isar.writeTxn(() async {
        await _isar.medicationIsars.put(record);
      });

      _logger.info(
          'Successfully added/updated medication record for cattle $cattleId');
    } catch (e) {
      _logger.severe(
          'Error adding/updating medication record for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to add/update medication record', e.toString());
    }
  }

  /// Deletes a medication record for a specific cattle
  Future<void> deleteMedication(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Record ID is required');
      }

      // Find the medication by record ID (businessId)
      final medication = await _isar.medicationIsars
          .filter()
          .businessIdEqualTo(recordId)
          .findFirst();

      if (medication == null) {
        throw RecordNotFoundException(
            'Medication record not found with ID: $recordId');
      }

      // Store cattle ID before deletion for notification
      final cattleId = medication.cattleId;

      await _isar.writeTxn(() async {
        await _isar.medicationIsars.delete(medication.id);
      });

      _logger.info('Successfully deleted medication record: $recordId');

      // Notify stream listeners about the medication deletion
      final streamService = GetIt.instance<StreamService>();
      streamService.handleNotification('medication_records', 'delete', cattleId ?? '', {
        'action': 'delete',
        'cattleId': cattleId,
        'recordId': recordId,
      });
    } catch (e) {
      _logger.severe('Error deleting medication record $recordId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to delete medication record', e.toString());
    }
  }

  /// Validates a medication record
  void _validateMedicationRecord(MedicationIsar record) {
    if (record.startDate == null) {
      throw ValidationException('Medication record must have a date');
    }

    if (record.name == null || record.name!.trim().isEmpty) {
      throw ValidationException(
          'Medication record must have a medication name');
    }

    if (record.dosage == null || record.dosage!.trim().isEmpty) {
      throw ValidationException('Medication record must have a dosage');
    }
  }

  //=== TREATMENT RECORDS ===//

  /// Retrieves treatments for a specific cattle
  Future<List<TreatmentIsar>> getTreatmentsForCattle(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.treatmentIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving treatments for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException('Failed to retrieve treatments', e.toString());
    }
  }

  /// Retrieves all treatment records across all cattle
  Future<List<TreatmentIsar>> getAllTreatments() async {
    try {
      return await _isar.treatmentIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all treatments', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to retrieve all treatments', e.toString());
    }
  }

  /// Adds or updates a treatment record for a specific cattle
  Future<void> addOrUpdateTreatment(
      String cattleId, TreatmentIsar record) async {
    try {
      _logger.info('🔵 Starting addOrUpdateTreatment for cattle: $cattleId');
      _logger.info('🔵 Treatment record data: date=${record.date}, treatment=${record.treatment}, businessId=${record.businessId}');

      if (cattleId.isEmpty) {
        _logger.severe('🔴 Cattle ID is empty');
        throw ValidationException('Cattle ID is required');
      }

      // Validate the record
      _logger.info('🔵 Validating treatment record...');
      _validateTreatmentRecord(record);
      _logger.info('🔵 Treatment record validation passed');

      // Set cattleId
      record.cattleId = cattleId;
      _logger.info('🔵 Set cattleId on record: $cattleId');

      _logger.info('🔵 Starting treatment database transaction...');
      await _isar.writeTxn(() async {
        await _isar.treatmentIsars.put(record);
        _logger.info('🔵 Treatment record saved to database');
      });

      _logger.info('🟢 Successfully added/updated treatment record for cattle $cattleId');

      // Notify stream listeners about the treatment change
      final streamService = GetIt.instance<StreamService>();
      streamService.handleNotification('treatmentRecords', 'add', cattleId, {
        'action': 'add',
        'cattleId': cattleId,
        'record': record.toMap(),
      });
    } catch (e, stackTrace) {
      _logger.severe('🔴 Error adding/updating treatment record for cattle $cattleId: $e');
      _logger.severe('🔴 Stack trace: $stackTrace');
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to add/update treatment record', e.toString());
    }
  }

  /// Migrates existing treatment records to have businessId
  Future<void> migrateTreatmentBusinessIds() async {
    try {
      _logger.info('🔄 Starting treatment businessId migration...');

      final treatmentsWithoutBusinessId = await _isar.treatmentIsars
          .filter()
          .businessIdIsNull()
          .or()
          .businessIdEqualTo('')
          .findAll();

      _logger.info('🔄 Found ${treatmentsWithoutBusinessId.length} treatments without businessId');

      if (treatmentsWithoutBusinessId.isNotEmpty) {
        await _isar.writeTxn(() async {
          for (var treatment in treatmentsWithoutBusinessId) {
            // Generate a new businessId
            final newBusinessId = 'treatment_${treatment.cattleId}_${DateTime.now().millisecondsSinceEpoch}_${treatment.id}';
            treatment.businessId = newBusinessId;
            await _isar.treatmentIsars.put(treatment);
            _logger.info('🔄 Migrated treatment ID ${treatment.id} -> businessId: $newBusinessId');
          }
        });

        _logger.info('🟢 Successfully migrated ${treatmentsWithoutBusinessId.length} treatment records');
      } else {
        _logger.info('🔄 No treatments need migration');
      }
    } catch (e) {
      _logger.severe('🔴 Error during treatment businessId migration: $e');
    }
  }

  /// Deletes a treatment record
  Future<void> deleteTreatment(String recordId) async {
    try {
      _logger.info('🔵 Starting deleteTreatment with recordId: "$recordId"');
      _logger.info('🔵 RecordId type: ${recordId.runtimeType}');
      _logger.info('🔵 RecordId length: ${recordId.length}');
      _logger.info('🔵 RecordId isEmpty: ${recordId.isEmpty}');

      if (recordId.isEmpty) {
        _logger.severe('🔴 Record ID is empty - throwing ValidationException');
        throw ValidationException('Record ID is required');
      }

      TreatmentIsar? treatment;

      // First try to find by businessId
      _logger.info('🔵 Searching for treatment with businessId: "$recordId"');
      treatment = await _isar.treatmentIsars
          .filter()
          .businessIdEqualTo(recordId)
          .findFirst();

      _logger.info('🔵 BusinessId search result: ${treatment != null ? 'FOUND' : 'NOT FOUND'}');

      // If not found by businessId, try by database ID (for legacy records)
      if (treatment == null) {
        _logger.info('🔵 Attempting fallback search by database ID...');
        final databaseId = int.tryParse(recordId);
        if (databaseId != null) {
          treatment = await _isar.treatmentIsars.get(databaseId);
          _logger.info('🔵 Database ID search result: ${treatment != null ? 'FOUND' : 'NOT FOUND'}');

          if (treatment != null) {
            _logger.warning('⚠️ Found treatment by database ID instead of businessId!');
            _logger.warning('⚠️ This indicates a legacy record without businessId');
          }
        }
      }

      if (treatment != null) {
        _logger.info('🔵 Found treatment details:');
        _logger.info('🔵   - Database ID: ${treatment.id}');
        _logger.info('🔵   - Business ID: ${treatment.businessId}');
        _logger.info('🔵   - Cattle ID: ${treatment.cattleId}');
        _logger.info('🔵   - Treatment: ${treatment.treatment}');
        _logger.info('🔵   - Date: ${treatment.date}');
      }

      if (treatment == null) {
        _logger.severe('🔴 Treatment record not found with ID: "$recordId" (tried both businessId and database ID)');
        throw RecordNotFoundException(
            'Treatment record not found with ID: $recordId');
      }

      // Store cattle ID before deletion for notification
      final cattleId = treatment.cattleId;
      _logger.info('🔵 Cattle ID for notification: $cattleId');

      _logger.info('🔵 Starting database transaction to delete treatment...');
      await _isar.writeTxn(() async {
        final deleteResult = await _isar.treatmentIsars.delete(treatment!.id);
        _logger.info('🔵 Delete transaction result: $deleteResult');
      });

      _logger.info('🟢 Successfully deleted treatment record: $recordId');

      // Notify stream listeners about the treatment deletion
      _logger.info('🔵 Sending stream notification...');
      final streamService = GetIt.instance<StreamService>();
      streamService.handleNotification('treatmentRecords', 'delete', cattleId ?? '', {
        'action': 'delete',
        'cattleId': cattleId,
        'recordId': recordId,
      });
      _logger.info('🔵 Stream notification sent successfully');

    } catch (e, stackTrace) {
      _logger.severe('🔴 Error deleting treatment record: $recordId');
      _logger.severe('🔴 Error type: ${e.runtimeType}');
      _logger.severe('🔴 Error message: $e');
      _logger.severe('🔴 Stack trace: $stackTrace');

      if (e is DatabaseException || e is ValidationException || e is RecordNotFoundException) {
        rethrow;
      }
      throw DatabaseException(
          'Failed to delete treatment record', e.toString());
    }
  }

  /// Validates a treatment record
  void _validateTreatmentRecord(TreatmentIsar record) {
    _logger.info('🔵 Validating treatment record fields...');

    if (record.date == null) {
      _logger.severe('🔴 Treatment validation failed: Date is null');
      throw ValidationException('Treatment date is required');
    }
    _logger.info('🔵 Treatment date validation passed: ${record.date}');

    if (record.treatment == null || record.treatment!.trim().isEmpty) {
      _logger.severe('🔴 Treatment validation failed: Treatment description is empty or null');
      throw ValidationException('Treatment description is required');
    }
    _logger.info('🔵 Treatment description validation passed: ${record.treatment}');

    _logger.info('🟢 All treatment record validations passed');
  }

  //=== VACCINATION RECORDS ===//

  /// Retrieves vaccinations for a specific cattle
  Future<List<VaccinationIsar>> getVaccinationsForCattle(
      String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.vaccinationIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving vaccinations for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException('Failed to retrieve vaccinations', e.toString());
    }
  }

  /// Retrieves all vaccination records across all cattle
  Future<List<VaccinationIsar>> getAllVaccinations() async {
    try {
      return await _isar.vaccinationIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all vaccinations', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to retrieve all vaccinations', e.toString());
    }
  }

  /// Adds or updates a vaccination record for a specific cattle
  /// Returns the saved vaccination with its ID
  Future<VaccinationIsar> addOrUpdateVaccination(
      String cattleId, VaccinationIsar record) async {
    try {
      _logger.info('🔵 Starting addOrUpdateVaccination for cattle: $cattleId');
      _logger.info('🔵 Vaccination record data: date=${record.date}, vaccineName=${record.vaccineName}, recordId=${record.recordId}');

      // Validate required fields
      _logger.info('🔵 Validating vaccination record...');
      _validateVaccinationRecord(record);
      _logger.info('🔵 Vaccination record validation passed');

      // Ensure cattleId is set on the record
      record.cattleId = cattleId;
      _logger.info('🔵 Set cattleId on vaccination record: $cattleId');

      late int recordId;
      _logger.info('🔵 Starting vaccination database transaction...');
      await _isar.writeTxn(() async {
        recordId = await _isar.vaccinationIsars.put(record);
        _logger.info('🔵 Vaccination record saved with database ID: $recordId');
      });

      _logger.info('🔵 Fetching saved vaccination record...');
      // Fetch the saved record to ensure we have the complete object with ID
      final savedVaccination = await _isar.vaccinationIsars.get(recordId);
      if (savedVaccination == null) {
        _logger.severe('🔴 Failed to retrieve saved vaccination with ID: $recordId');
        throw DatabaseException('Failed to retrieve saved vaccination', 'Vaccination not found after saving');
      }

      _logger.info('🟢 Successfully added/updated vaccination record for cattle $cattleId');
      _logger.info('🟢 Saved vaccination details: recordId=${savedVaccination.recordId}, vaccineName=${savedVaccination.vaccineName}');

      // Notify stream listeners about the vaccination change
      final streamService = GetIt.instance<StreamService>();
      streamService.handleNotification('vaccinationRecords', 'add', cattleId, {
        'action': 'add',
        'cattleId': cattleId,
        'recordId': savedVaccination.recordId,
        'record': savedVaccination.toMap(),
      });

      return savedVaccination;
    } catch (e, stackTrace) {
      _logger.severe('🔴 Error adding/updating vaccination record for cattle $cattleId: $e');
      _logger.severe('🔴 Stack trace: $stackTrace');
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to add/update vaccination record', e.toString());
    }
  }

  /// Migrates existing vaccination records to have recordId
  Future<void> migrateVaccinationRecordIds() async {
    try {
      _logger.info('🔄 Starting vaccination recordId migration...');

      final vaccinationsWithoutRecordId = await _isar.vaccinationIsars
          .filter()
          .recordIdIsNull()
          .or()
          .recordIdEqualTo('')
          .findAll();

      _logger.info('🔄 Found ${vaccinationsWithoutRecordId.length} vaccinations without recordId');

      if (vaccinationsWithoutRecordId.isNotEmpty) {
        await _isar.writeTxn(() async {
          for (var vaccination in vaccinationsWithoutRecordId) {
            // Generate a new recordId
            final newRecordId = 'vaccination_${vaccination.cattleId}_${DateTime.now().millisecondsSinceEpoch}_${vaccination.id}';
            vaccination.recordId = newRecordId;
            await _isar.vaccinationIsars.put(vaccination);
            _logger.info('🔄 Migrated vaccination ID ${vaccination.id} -> recordId: $newRecordId');
          }
        });

        _logger.info('🟢 Successfully migrated ${vaccinationsWithoutRecordId.length} vaccination records');
      } else {
        _logger.info('🔄 No vaccinations need migration');
      }
    } catch (e) {
      _logger.severe('🔴 Error during vaccination recordId migration: $e');
    }
  }

  /// Deletes a vaccination record
  Future<void> deleteVaccination(String recordId) async {
    try {
      print('🔵 HealthHandler.deleteVaccination called with recordId: "$recordId"');
      print('🔵 RecordId type: ${recordId.runtimeType}');
      print('🔵 RecordId length: ${recordId.length}');
      print('🔵 RecordId isEmpty: ${recordId.isEmpty}');

      _logger.info('🔵 Starting deleteVaccination with recordId: "$recordId"');
      _logger.info('🔵 RecordId type: ${recordId.runtimeType}');
      _logger.info('🔵 RecordId length: ${recordId.length}');
      _logger.info('🔵 RecordId isEmpty: ${recordId.isEmpty}');

      if (recordId.isEmpty) {
        print('🔴 Record ID is empty - throwing ValidationException');
        _logger.severe('🔴 Record ID is empty - throwing ValidationException');
        throw ValidationException('Record ID is required');
      }

      VaccinationIsar? vaccination;

      // First try to find by recordId
      print('🔵 Searching for vaccination with recordId: "$recordId"');
      _logger.info('🔵 Searching for vaccination with recordId: "$recordId"');
      vaccination = await _isar.vaccinationIsars
          .filter()
          .recordIdEqualTo(recordId)
          .findFirst();

      print('🔵 RecordId search result: ${vaccination != null ? 'FOUND' : 'NOT FOUND'}');
      _logger.info('🔵 RecordId search result: ${vaccination != null ? 'FOUND' : 'NOT FOUND'}');

      // If not found by recordId, try by database ID (for legacy records)
      if (vaccination == null) {
        print('🔵 Attempting fallback search by database ID...');
        _logger.info('🔵 Attempting fallback search by database ID...');
        final databaseId = int.tryParse(recordId);
        if (databaseId != null) {
          vaccination = await _isar.vaccinationIsars.get(databaseId);
          print('🔵 Database ID search result: ${vaccination != null ? 'FOUND' : 'NOT FOUND'}');
          _logger.info('🔵 Database ID search result: ${vaccination != null ? 'FOUND' : 'NOT FOUND'}');

          if (vaccination != null) {
            print('⚠️ Found vaccination by database ID instead of recordId!');
            print('⚠️ This indicates a legacy record without recordId');
            _logger.warning('⚠️ Found vaccination by database ID instead of recordId!');
            _logger.warning('⚠️ This indicates a legacy record without recordId');
          }
        }
      }

      if (vaccination != null) {
        print('🔵 Found vaccination details:');
        print('🔵   - Database ID: ${vaccination.id}');
        print('🔵   - Record ID: ${vaccination.recordId}');
        print('🔵   - Cattle ID: ${vaccination.cattleId}');
        print('🔵   - Vaccine: ${vaccination.vaccineName}');
        print('🔵   - Date: ${vaccination.date}');

        _logger.info('🔵 Found vaccination details:');
        _logger.info('🔵   - Database ID: ${vaccination.id}');
        _logger.info('🔵   - Record ID: ${vaccination.recordId}');
        _logger.info('🔵   - Cattle ID: ${vaccination.cattleId}');
        _logger.info('🔵   - Vaccine: ${vaccination.vaccineName}');
        _logger.info('🔵   - Date: ${vaccination.date}');
      }

      if (vaccination == null) {
        print('🔴 Vaccination record not found with ID: "$recordId" (tried both recordId and database ID)');
        _logger.severe('🔴 Vaccination record not found with ID: "$recordId" (tried both recordId and database ID)');
        throw RecordNotFoundException(
            'Vaccination record not found with ID: $recordId');
      }

      // Store cattle ID before deletion for notification
      final cattleId = vaccination.cattleId;
      print('🔵 Cattle ID for notification: $cattleId');
      _logger.info('🔵 Cattle ID for notification: $cattleId');

      print('🔵 Starting database transaction to delete vaccination...');
      _logger.info('🔵 Starting database transaction to delete vaccination...');
      await _isar.writeTxn(() async {
        final deleteResult = await _isar.vaccinationIsars.delete(vaccination!.id);
        print('🔵 Delete transaction result: $deleteResult');
        _logger.info('🔵 Delete transaction result: $deleteResult');
      });

      print('🟢 Successfully deleted vaccination record: $recordId');
      _logger.info('🟢 Successfully deleted vaccination record: $recordId');

      // Notify stream listeners about the vaccination deletion
      print('🔵 Sending stream notification...');
      _logger.info('🔵 Sending stream notification...');
      final streamService = GetIt.instance<StreamService>();
      streamService.handleNotification('vaccination_records', 'delete', cattleId ?? '', {
        'action': 'delete',
        'cattleId': cattleId,
        'recordId': recordId,
      });
      print('🔵 Stream notification sent successfully');
      _logger.info('🔵 Stream notification sent successfully');

    } catch (e, stackTrace) {
      print('🔴 Error deleting vaccination record: $recordId');
      print('🔴 Error type: ${e.runtimeType}');
      print('🔴 Error message: $e');
      print('🔴 Stack trace: $stackTrace');

      _logger.severe('🔴 Error deleting vaccination record: $recordId');
      _logger.severe('🔴 Error type: ${e.runtimeType}');
      _logger.severe('🔴 Error message: $e');
      _logger.severe('🔴 Stack trace: $stackTrace');

      if (e is DatabaseException || e is ValidationException || e is RecordNotFoundException) {
        rethrow;
      }
      throw DatabaseException(
          'Failed to delete vaccination record', e.toString());
    }
  }

  /// Validates a vaccination record
  void _validateVaccinationRecord(VaccinationIsar record) {
    _logger.info('🔵 Validating vaccination record fields...');

    if (record.date == null) {
      _logger.severe('🔴 Vaccination validation failed: Date is null');
      throw ValidationException('Vaccination date is required');
    }
    _logger.info('🔵 Vaccination date validation passed: ${record.date}');

    if (record.vaccineName == null || record.vaccineName!.trim().isEmpty) {
      _logger.severe('🔴 Vaccination validation failed: Vaccine name is empty or null');
      throw ValidationException('Vaccine name is required');
    }
    _logger.info('🔵 Vaccine name validation passed: ${record.vaccineName}');

    _logger.info('🟢 All vaccination record validations passed');
  }
}
