import 'package:flutter/material.dart';

class EmptyState extends StatelessWidget {
  final IconData icon;
  final String message;
  final String subtitle;
  final Color? color;
  final Widget? action;
  final bool useCircularBackground;
  final double iconSize;
  final EdgeInsets padding;

  const EmptyState({
    Key? key,
    required this.icon,
    required this.message,
    required this.subtitle,
    this.color,
    this.action,
    this.useCircularBackground = true,
    this.iconSize = 48,
    this.padding = const EdgeInsets.all(24.0),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.colorScheme.primary;

    return Center(
      child: Padding(
        padding: padding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (useCircularBackground)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: effectiveColor.withAlpha(26), // 0.1 * 255 = 26
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: iconSize,
                  color: effectiveColor,
                ),
              )
            else
              Icon(
                icon,
                size: iconSize + 16, // Slightly larger when no background
                color: effectiveColor.withAlpha(128), // 0.5 * 255 = 128
              ),
            const SizedBox(height: 16),
            Text(
              message,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[
              const SizedBox(height: 16),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}

// Specialized empty state for milk module that follows milk tab design patterns
class MilkEmptyState extends StatelessWidget {
  final IconData icon;
  final String message;
  final String subtitle;
  final Color color;
  final Widget? action;
  final EdgeInsets padding;

  const MilkEmptyState({
    Key? key,
    required this.icon,
    required this.message,
    required this.subtitle,
    required this.color,
    this.action,
    this.padding = const EdgeInsets.all(24.0),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: icon,
      message: message,
      subtitle: subtitle,
      color: color,
      action: action,
      useCircularBackground: true,
      iconSize: 48,
      padding: padding,
    );
  }
}

// Chart-specific empty state
class ChartEmptyState extends StatelessWidget {
  final IconData icon;
  final String message;
  final String subtitle;
  final Color color;
  final double height;

  const ChartEmptyState({
    Key? key,
    required this.icon,
    required this.message,
    required this.subtitle,
    required this.color,
    this.height = 250,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: MilkEmptyState(
        icon: icon,
        message: message,
        subtitle: subtitle,
        color: color,
        padding: const EdgeInsets.all(16.0),
      ),
    );
  }
}
