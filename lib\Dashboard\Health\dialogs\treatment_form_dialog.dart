import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/treatment_isar.dart';
import 'unified_health_form_dialog.dart';

/// Legacy wrapper for TreatmentFormDialog
/// Maintains backward compatibility while using the new unified form
class TreatmentFormDialog extends StatelessWidget {
  final TreatmentIsar? treatment;
  final List<CattleIsar> cattle;
  final String? cattleId;
  final Future<void> Function(TreatmentIsar)? onSave;

  const TreatmentFormDialog({
    Key? key,
    this.treatment,
    required this.cattle,
    this.cattleId,
    this.onSave,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnifiedHealthFormDialog(
      formType: 'treatment',
      cattle: cattle,
      initialCattleId: cattleId,
      existingRecord: treatment,
      onSaved: () {
        // The unified dialog handles the save operation internally
        // This callback is called after successful save
      },
    );
  }
}
