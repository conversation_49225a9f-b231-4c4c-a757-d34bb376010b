import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';

import '../models/milk_record_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../services/streams/stream_service.dart';

/// Consolidated handler for all Milk Records module database operations
class MilkHandler {
  static final Logger _logger = Logger('MilkHandler');
  final IsarService _isarService;

  // Singleton instance
  static final MilkHandler _instance = MilkHandler._internal();
  static MilkHandler get instance => _instance;

  // Private constructor
  MilkHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== MILK RECORDS ===//

  /// Get all milk records
  Future<List<MilkRecordIsar>> getAllMilkRecords() async {
    try {
      return await _isar.milkRecordIsars
          .where()
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting all milk records: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle (by tagId - consistent with other modules)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattle(String cattleTagId) async {
    try {
      if (cattleTagId.isEmpty) {
        throw ValidationException('Cattle Tag ID is required');
      }

      return await _isar.milkRecordIsars
          .filter()
          .cattleTagIdEqualTo(cattleTagId)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleTagId: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle (by businessId - for backward compatibility)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleByBusinessId(String cattleBusinessId) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle Business ID is required');
      }

      return await _isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Add a new milk record
  /// Returns the saved record with its ID
  Future<MilkRecordIsar> addMilkRecord(MilkRecordIsar record) async {
    try {
      late int recordId;
      await _isar.writeTxn(() async {
        recordId = await _isar.milkRecordIsars.put(record);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedRecord = await _isar.milkRecordIsars.get(recordId);
      if (savedRecord == null) {
        throw DatabaseException('Failed to retrieve saved record', 'Record not found after saving');
      }

      _logger.info('Added new milk record: ${savedRecord.businessId}, ID: $recordId');

      // Notify stream listeners about the milk record addition
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyMilkChange({
        'action': 'add',
        'cattleId': savedRecord.cattleBusinessId,
        'recordId': savedRecord.businessId,
        'record': savedRecord.toMap(),
      });

      return savedRecord;
    } catch (e) {
      _logger.severe('Error adding milk record: $e');
      throw DatabaseException('Failed to add milk record', e.toString());
    }
  }

  /// Update an existing milk record
  /// Returns the saved record with its ID
  Future<MilkRecordIsar> updateMilkRecord(MilkRecordIsar record) async {
    try {
      late int recordId;
      await _isar.writeTxn(() async {
        recordId = await _isar.milkRecordIsars.put(record);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedRecord = await _isar.milkRecordIsars.get(recordId);
      if (savedRecord == null) {
        throw DatabaseException('Failed to retrieve saved record', 'Record not found after saving');
      }

      _logger.info('Updated milk record: ${savedRecord.businessId}, ID: $recordId');

      // Notify stream listeners about the milk record update
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyMilkChange({
        'action': 'update',
        'cattleId': savedRecord.cattleBusinessId,
        'recordId': savedRecord.businessId,
        'record': savedRecord.toMap(),
      });

      return savedRecord;
    } catch (e) {
      _logger.severe('Error updating milk record: $e');
      throw DatabaseException('Failed to update milk record', e.toString());
    }
  }

  /// Delete a milk record
  Future<void> deleteMilkRecord(Id id) async {
    try {
      // Get record details before deletion for notification
      final recordToDelete = await _isar.milkRecordIsars.get(id);

      await _isar.writeTxn(() async {
        await _isar.milkRecordIsars.delete(id);
      });

      _logger.info('Deleted milk record: $id');

      // Notify stream listeners about the milk record deletion
      if (recordToDelete != null) {
        final streamService = GetIt.instance<StreamService>();
        streamService.notifyMilkChange({
          'action': 'delete',
          'cattleId': recordToDelete.cattleBusinessId,
          'recordId': recordToDelete.businessId,
        });
      }
    } catch (e) {
      _logger.severe('Error deleting milk record: $e');
      throw DatabaseException('Failed to delete milk record', e.toString());
    }
  }

  /// Get milk records for a date range
  Future<List<MilkRecordIsar>> getMilkRecordsForDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _isar.milkRecordIsars
          .filter()
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for date range: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in a date range (by tagId - consistent with other modules)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInDateRange(
    String cattleTagId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      if (cattleTagId.isEmpty) {
        throw ValidationException('Cattle Tag ID is required');
      }

      return await _isar.milkRecordIsars
          .filter()
          .cattleTagIdEqualTo(cattleTagId)
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleTagId in date range: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in a date range (by businessId - for backward compatibility)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInDateRangeByBusinessId(
    String cattleBusinessId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle Business ID is required');
      }

      return await _isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId in date range: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific date
  Future<List<MilkRecordIsar>> getMilkRecordsForDate(DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      return await getMilkRecordsForDateRange(startOfDay, endOfDay);
    } catch (e) {
      _logger.severe('Error getting milk records for date: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle on a specific date (by tagId)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleOnDate(
    String cattleTagId,
    DateTime date,
  ) async {
    try {
      if (cattleTagId.isEmpty) {
        throw ValidationException('Cattle Tag ID is required');
      }

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      return await getMilkRecordsForCattleInDateRange(cattleTagId, startOfDay, endOfDay);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleTagId on date: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle on a specific date (by businessId) - for backward compatibility
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleOnDateByBusinessId(
    String cattleBusinessId,
    DateTime date,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle Business ID is required');
      }

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      return await getMilkRecordsForCattleInDateRangeByBusinessId(cattleBusinessId, startOfDay, endOfDay);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId on date: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N days (by tagId)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNDays(
    String cattleTagId,
    int days,
  ) async {
    try {
      if (cattleTagId.isEmpty) {
        throw ValidationException('Cattle Tag ID is required');
      }

      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));
      return await getMilkRecordsForCattleInDateRange(cattleTagId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleTagId in last $days days: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N months (by tagId)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNMonths(
    String cattleTagId,
    int months,
  ) async {
    try {
      if (cattleTagId.isEmpty) {
        throw ValidationException('Cattle Tag ID is required');
      }

      final endDate = DateTime.now();
      final startDate = DateTime(endDate.year, endDate.month - months, endDate.day);
      return await getMilkRecordsForCattleInDateRange(cattleTagId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleTagId in last $months months: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N years (by tagId)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNYears(
    String cattleTagId,
    int years,
  ) async {
    try {
      if (cattleTagId.isEmpty) {
        throw ValidationException('Cattle Tag ID is required');
      }

      final endDate = DateTime.now();
      final startDate = DateTime(endDate.year - years, endDate.month, endDate.day);
      return await getMilkRecordsForCattleInDateRange(cattleTagId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleTagId in last $years years: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N weeks (by tagId)
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNWeeks(
    String cattleTagId,
    int weeks,
  ) async {
    try {
      if (cattleTagId.isEmpty) {
        throw ValidationException('Cattle Tag ID is required');
      }

      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: weeks * 7));
      return await getMilkRecordsForCattleInDateRange(cattleTagId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleTagId in last $weeks weeks: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }
}