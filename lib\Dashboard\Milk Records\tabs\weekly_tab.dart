import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../widgets/empty_state.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

class WeeklyTab extends StatefulWidget {
  final List<MilkRecordIsar> filteredRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final DateTime? selectedWeek;
  final Function(BuildContext, List<DateTime>) selectWeek;

  const WeeklyTab({
    Key? key,
    required this.filteredRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    this.selectedWeek,
    required this.selectWeek,
  }) : super(key: key);

  @override
  State<WeeklyTab> createState() => _WeeklyTabState();
}

class _WeeklyTabState extends State<WeeklyTab> {
  DateTime? _selectedWeek;

  // Milk tab color palette - matching the main milk tab colors
  static const _primaryColor = Color(0xFF1976D2); // Blue (Weekly tab color)
  static const _secondaryColor = Color(0xFF2E7D32); // Green (Daily)
  static const _accentColor = Color(0xFF7B1FA2); // Purple (Evening)
  static const _redColor = Color(0xFFD32F2F); // Red (Total)
  static const _cardShadowColor = Color(0x1A000000);
  static const _cardBorderRadius = 12.0; // Match milk tab border radius

  // Animation duration
  static const _animDuration = Duration(milliseconds: 300);

  // Performance optimization: Caching


  @override
  void initState() {
    super.initState();
    _selectedWeek = widget.selectedWeek;
  }

  @override
  void didUpdateWidget(WeeklyTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedWeek != oldWidget.selectedWeek) {
      _selectedWeek = widget.selectedWeek;
    }

    // Invalidate cache if filtered records changed
    if (widget.filteredRecords != oldWidget.filteredRecords) {
      _invalidateCache();
    }
  }

  // Cache management
  void _invalidateCache() {
    // Cache cleared for refresh
  }



  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    String? subtitle,
    Color iconColor = Colors.white,
    Color textColor = Colors.black87,
    Color valueColor = Colors.black87,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and icon row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 4,
                child: Text(
                  title,
                  style: TextStyle(
                    color: textColor.withAlpha(204), // 0.8 * 255 = 204
                    fontWeight: FontWeight.w700,
                    fontSize: 12,
                    letterSpacing: 0.5,
                  ),
                  softWrap: true,
                  maxLines: 2,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(38), // 0.15 * 255 = 38
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: iconColor, size: 16),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // Value with large font
          Text(
            value,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w900,
              color: valueColor,
              letterSpacing: -0.5,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: textColor.withAlpha(179), // 0.7 * 255 = 179
                fontWeight: FontWeight.w500,
              ),
              softWrap: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCattleListItem(
    CattleIsar? cattle,
    double value,
    double percentage, {
    bool showTrend = true,
    Color progressColor = Colors.blue,
  }) {
    final greyColor = Colors.grey.shade600;
    final cattleName = cattle?.name ?? 'Unknown';
    final tagId = cattle?.tagId ?? '';
    final displayName =
        (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                cattle?.animalTypeId == 'cow' ? Icons.emoji_nature : Icons.pets,
                color: progressColor.withAlpha(179), // 0.7 * 255 = 179
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: Text(
                  displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  '${value.toStringAsFixed(1)} L',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.end,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey.shade200,
                    color: progressColor,
                    minHeight: 5,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              if (showTrend)
                Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: percentage > 20 ? Colors.green : greyColor,
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardWithHeader(
    String title,
    IconData icon,
    Widget content, {
    Color headerColor = Colors.black87,
  }) {
    return Card(
      elevation: 4,
      shadowColor: _cardShadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_cardBorderRadius),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: headerColor.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(_cardBorderRadius),
                topRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: headerColor, size: 18),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: headerColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // Content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(_cardBorderRadius),
                bottomRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildWeekCard(
      DateTime weekStart, List<MilkRecordIsar> weekRecords, List<DateTime> weeks) {
    final weekEnd = weekStart.add(const Duration(days: 6));

    // Calculate weekly totals
    double totalProduction = 0;
    double morningTotal = 0;
    double eveningTotal = 0;
    final dailyProduction = <DateTime, double>{};
    final topCattleProduction = <String, double>{};

    // Initialize daily production for the entire week
    for (var i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      dailyProduction[day] = 0;
    }

    for (var record in weekRecords) {
      // Fix: Use consistent calculation method
      final recordTotal = (record.morningAmount ?? 0) + (record.eveningAmount ?? 0);
      totalProduction += recordTotal;
      morningTotal += record.morningAmount ?? 0;
      eveningTotal += record.eveningAmount ?? 0;

      // Add to daily production
      final day =
          DateTime(record.date?.year ?? 0, record.date?.month ?? 0, record.date?.day ?? 0);
      dailyProduction[day] = (dailyProduction[day] ?? 0) + recordTotal;

      // Track by cattle for top producers
      final tagId = record.cattleTagId ?? '';
      if (tagId.isNotEmpty) {
        topCattleProduction[tagId] =
            (topCattleProduction[tagId] ?? 0) + recordTotal;
      }
    }

    // Get top and bottom performers
    final sortedCattle = topCattleProduction.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final top3Cattle = sortedCattle.take(3).toList();
    final bottom3Cattle = sortedCattle.reversed.take(3).toList();

    // Calculate average daily production - FIXED: Use actual recording days
    final actualRecordingDays = dailyProduction.values.where((v) => v > 0).length;
    final avgDailyProduction = actualRecordingDays > 0
        ? totalProduction / actualRecordingDays
        : 0;

    // Also calculate theoretical 7-day average for comparison
    // Weekly average calculation for reference

    final morningPercentage =
        totalProduction > 0 ? (morningTotal / totalProduction * 100) : 0;
    final eveningPercentage =
        totalProduction > 0 ? (eveningTotal / totalProduction * 100) : 0;

    // Find days with significant drops (>20% below average) - FIXED: Use correct average
    final productionAlerts = dailyProduction.entries
        .where((entry) =>
            entry.value > 0 && entry.value < avgDailyProduction * 0.8)
        .toList();

    // Calculation details logged for debugging if needed

    return AnimatedContainer(
      duration: _animDuration,
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with week selection
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(_cardBorderRadius),
              boxShadow: const [
                BoxShadow(
                  color: _cardShadowColor,
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Week of ${DateFormat('MMM d').format(weekStart)}',
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.calendar_month,
                          color: _primaryColor),
                      onPressed: () => widget.selectWeek(context, weeks),
                      style: IconButton.styleFrom(
                        backgroundColor:
                            _primaryColor.withAlpha(26), // 0.1 * 255 = 26
                        padding: const EdgeInsets.all(8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${DateFormat('MMM d, yyyy').format(weekStart)} - ${DateFormat('MMM d, yyyy').format(weekEnd)}',
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.grey.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Key Stats Section
          _buildCardWithHeader(
            'Key Production Metrics',
            Icons.analytics,
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.local_drink,
                        iconColor: _primaryColor,
                        valueColor: _primaryColor,
                        title: 'TOTAL PRODUCTION',
                        value: '${totalProduction.toStringAsFixed(1)} L',
                        subtitle: 'Combined milk yield',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.equalizer,
                        iconColor: _secondaryColor,
                        valueColor: _secondaryColor,
                        title: 'AVERAGE DAILY',
                        value: '${avgDailyProduction.toStringAsFixed(1)} L',
                        subtitle: '$actualRecordingDays days with records',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.equalizer,
                        iconColor: _accentColor,
                        valueColor: _accentColor,
                        title: 'AVERAGE PER COW',
                        value:
                            '${(totalProduction / (topCattleProduction.keys.isNotEmpty ? topCattleProduction.keys.length : 1)).toStringAsFixed(1)} L',
                        subtitle: '${topCattleProduction.keys.length} cattle',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.star,
                        iconColor: Colors.green,
                        valueColor: Colors.green,
                        title: 'TOP PRODUCER',
                        value: top3Cattle.isNotEmpty
                            ? '${top3Cattle.first.value.toStringAsFixed(1)} L'
                            : 'N/A',
                        subtitle: top3Cattle.isNotEmpty
                            ? (() {
                                final cattle =
                                    widget.cattleMap[top3Cattle.first.key];
                                final cattleName = cattle?.name ?? 'Unknown';
                                final tagId = cattle?.tagId ?? '';
                                return (tagId.isNotEmpty)
                                    ? '$cattleName ($tagId)'
                                    : cattleName;
                              })()
                            : '',
                      ),
                    ),
                  ],
                ),
              ],
            ),
            headerColor: _primaryColor,
          ),
          const SizedBox(height: 16),

          // Morning/Evening Distribution Card
          _buildCardWithHeader(
            'Production Distribution',
            Icons.pie_chart,
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.wb_sunny_rounded, color: _primaryColor, size: 16), // Use blue for morning
                          SizedBox(width: 4),
                          Text(
                            'Morning',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: _primaryColor, // Blue for morning
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${morningTotal.toStringAsFixed(1)} L',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w900,
                          color: _primaryColor, // Blue for morning
                        ),
                      ),
                      Text(
                        '(${morningPercentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _primaryColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.nightlight_rounded,
                              color: _accentColor, size: 16), // Purple for evening
                          SizedBox(width: 4),
                          Text(
                            'Evening',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: _accentColor, // Purple for evening
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${eveningTotal.toStringAsFixed(1)} L',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w900,
                          color: _accentColor, // Purple for evening
                        ),
                      ),
                      Text(
                        '(${eveningPercentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _accentColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            headerColor: _secondaryColor,
          ),
          const SizedBox(height: 16),

          // Top Performers Card
          _buildCardWithHeader(
            'Top Producers',
            Icons.trending_up,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...top3Cattle.map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    progressColor: _secondaryColor, // Green for top performers
                  );
                }).toList(),
              ],
            ),
            headerColor: _secondaryColor, // Green for top performers
          ),
          const SizedBox(height: 16),

          // Bottom Performers Card
          _buildCardWithHeader(
            'Low Producers',
            Icons.trending_down,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...bottom3Cattle.map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    progressColor: _redColor, // Red for low performers (avoiding orange)
                  );
                }).toList(),
              ],
            ),
            headerColor: _redColor, // Red for low performers (avoiding orange)
          ),
          const SizedBox(height: 16),

          // Production Alerts Card
          _buildCardWithHeader(
            'Production Alerts',
            Icons.warning_amber_rounded,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _redColor.withAlpha(26), // 0.1 * 255 = 26
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: _redColor, size: 18),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Days with production below 80% of weekly average',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                if (productionAlerts.isNotEmpty)
                  ...productionAlerts.map((entry) {
                    final day = entry.key;
                    final production = entry.value;
                    final dropPercentage = ((avgDailyProduction - production) /
                        avgDailyProduction *
                        100);
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        children: [
                          Text(
                            DateFormat('EEEE, MMM d').format(day),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${production.toStringAsFixed(1)} L ',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '(${dropPercentage.toStringAsFixed(1)}% below avg)',
                            style: const TextStyle(color: _redColor),
                          ),
                        ],
                      ),
                    );
                  }).toList()
                else
                  const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: Text(
                      'No days with significantly low production this week.',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
              ],
            ),
            headerColor: _redColor,
          ),
          const SizedBox(height: 16),

          // Weekly Chart Card
          _buildCardWithHeader(
            'Weekly Production Trend',
            Icons.show_chart,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                SizedBox(
                  height: 300,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.only(left: 5, bottom: 8),
                        child: Text(
                          'Production (L)',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: _redColor, // Use red instead of orange
                          ),
                        ),
                      ),
                      Expanded(
                        child: LineChart(
                          LineChartData(
                            gridData: FlGridData(
                              show: true,
                              drawVerticalLine: true,
                              horizontalInterval: 3,
                              verticalInterval: 1,
                              getDrawingHorizontalLine: (value) {
                                return FlLine(
                                  color: Colors.grey.shade200,
                                  strokeWidth: 1,
                                );
                              },
                              getDrawingVerticalLine: (value) {
                                return FlLine(
                                  color: Colors.grey.shade200,
                                  strokeWidth: 1,
                                );
                              },
                            ),
                            titlesData: FlTitlesData(
                              leftTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  reservedSize: 40,
                                  interval: 3,
                                  getTitlesWidget: (value, meta) {
                                    // Only show titles at intervals of 3
                                    if (value % 3 != 0 && value != 0) {
                                      return const SizedBox.shrink();
                                    }
                                    return Text(
                                      value.toInt().toString(),
                                      style: const TextStyle(
                                        color: _redColor, // Use red instead of orange
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    );
                                  },
                                ),
                              ),
                              bottomTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  reservedSize: 24,
                                  getTitlesWidget: (value, meta) {
                                    if (value < 0 || value > 6) {
                                      return const Text('');
                                    }
                                    final day = weekStart
                                        .add(Duration(days: value.toInt()));
                                    return Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        DateFormat('E').format(day),
                                        style: const TextStyle(
                                          color: _secondaryColor,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              rightTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                              topTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                            ),
                            borderData: FlBorderData(
                              show: true,
                              border: Border.all(
                                color: Colors.grey.shade200,
                                width: 1,
                              ),
                            ),
                            minX: 0,
                            maxX: 6,
                            minY: 0,
                            maxY: dailyProduction.values.isEmpty
                                ? 10
                                : (dailyProduction.values
                                        .reduce((a, b) => a > b ? a : b) *
                                    1.2),
                            lineTouchData: LineTouchData(
                              enabled: true,
                              handleBuiltInTouches: true,
                              touchTooltipData: LineTouchTooltipData(
                                tooltipRoundedRadius: 8,
                                tooltipPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                tooltipMargin: 8,
                                fitInsideHorizontally: true,
                                fitInsideVertically: true,
                                getTooltipItems:
                                    (List<LineBarSpot> touchedBarSpots) {
                                  return touchedBarSpots.map((barSpot) {
                                    final day = weekStart
                                        .add(Duration(days: barSpot.x.toInt()));
                                    return LineTooltipItem(
                                      '${DateFormat('EEE, MMM d').format(day)}\n${barSpot.y.toStringAsFixed(1)} L',
                                      const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                      textAlign: TextAlign.center,
                                    );
                                  }).toList();
                                },
                              ),
                            ),
                            lineBarsData: [
                              LineChartBarData(
                                spots: dailyProduction.entries.map((entry) {
                                  final daysDiff =
                                      entry.key.difference(weekStart).inDays;
                                  return FlSpot(
                                      daysDiff.toDouble(), entry.value);
                                }).toList(),
                                isCurved: true,
                                curveSmoothness: 0.3,
                                preventCurveOverShooting: true,
                                preventCurveOvershootingThreshold: 5.0,
                                color: _primaryColor,
                                barWidth: 3,
                                isStrokeCapRound: true,
                                dotData: FlDotData(
                                  show: true,
                                  getDotPainter:
                                      (spot, percent, barData, index) {
                                    return FlDotCirclePainter(
                                      radius: 6,
                                      color: _primaryColor,
                                      strokeWidth: 3,
                                      strokeColor: Colors.white,
                                    );
                                  },
                                  checkToShowDot: (spot, barData) => true,
                                ),
                                belowBarData: BarAreaData(
                                  show: true,
                                  color: _primaryColor
                                      .withAlpha(26), // 0.1 * 255 = 26
                                  cutOffY: 0,
                                  applyCutOffY: true,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(top: 16, bottom: 8),
                        child: Center(
                          child: Text(
                            'Day of Week',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: _secondaryColor,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
            headerColor: _primaryColor,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.filteredRecords.isEmpty) {
      return const MilkEmptyState(
        icon: Icons.filter_alt_off_outlined,
        message: 'No Records Found',
        subtitle: 'No records found for the selected filters',
        color: _primaryColor,
      );
    }

    // Group records by week
    final recordsByWeek = <DateTime, List<MilkRecordIsar>>{};
    for (var record in widget.filteredRecords) {
      if (record.date != null) {
        // Get the start of the week (Monday)
        final date = record.date!;
        final weekStart = DateTime(
          date.year,
          date.month,
          date.day,
        ).subtract(Duration(days: date.weekday - 1));
        recordsByWeek.putIfAbsent(weekStart, () => []).add(record);
      }
    }

    // Sort weeks in descending order
    final weeks = recordsByWeek.keys.toList()..sort((a, b) => b.compareTo(a));

    // If a week is selected, show only that week
    if (_selectedWeek != null) {
      final selectedWeekStart = DateTime(
          _selectedWeek!.year, _selectedWeek!.month, _selectedWeek!.day);

      if (recordsByWeek.containsKey(selectedWeekStart)) {
        return SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWeekCard(selectedWeekStart,
                    recordsByWeek[selectedWeekStart]!, weeks),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedWeek = null;
                      });
                    },
                    icon: const Icon(Icons.list),
                    label: const Text('Show All Weeks'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        // If the selected week doesn't have records, show a message
        return MilkEmptyState(
          icon: Icons.date_range_outlined,
          message: 'No Records for Selected Week',
          subtitle: 'No records found for the selected week',
          color: _primaryColor,
          action: ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedWeek = null;
              });
            },
            icon: const Icon(Icons.list),
            label: const Text('Show All Weeks'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      }
    }

    return SingleChildScrollView(
      child: Padding(
        padding:
            const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...weeks.map((weekStart) {
              final weekRecords = recordsByWeek[weekStart]!;
              return _buildWeekCard(weekStart, weekRecords, weeks);
            }).toList(),
          ],
        ),
      ),
    );
  }
}
