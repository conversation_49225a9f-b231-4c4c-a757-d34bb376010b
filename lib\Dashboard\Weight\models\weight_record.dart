import 'package:isar/isar.dart';

part 'weight_record.g.dart';

@collection
class WeightRecord {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String recordId;

  @Index()
  String cattleId;

  @Index()
  DateTime date;

  double weight;

  String notes;

  WeightRecord({
    required this.recordId,
    required this.cattleId,
    required this.date,
    required this.weight,
    required this.notes,
  });
}
