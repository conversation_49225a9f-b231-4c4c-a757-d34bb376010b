import 'dart:async';
import 'package:logging/logging.dart';
import '../../../services/database/database_helper.dart';
import '../models/health_record_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Service for health analytics and reporting
class HealthAnalyticsService {
  static final Logger _logger = Logger('HealthAnalyticsService');
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  /// Get comprehensive health analytics data
  Future<HealthAnalyticsData> getHealthAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final now = DateTime.now();
      final start = startDate ?? DateTime(now.year - 1, now.month, now.day);
      final end = endDate ?? now;

      _logger.info('Generating health analytics from $start to $end');

      // Get all data
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      final allHealthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      final allTreatments = await _dbHelper.healthHandler.getAllTreatments();
      final allVaccinations = await _dbHelper.healthHandler.getAllVaccinations();

      // Filter by date range
      final healthRecords = allHealthRecords.where((r) => 
        r.date != null && r.date!.isAfter(start) && r.date!.isBefore(end)
      ).toList();

      final treatments = allTreatments.where((t) => 
        t.date != null && t.date!.isAfter(start) && t.date!.isBefore(end)
      ).toList();

      final vaccinations = allVaccinations.where((v) => 
        v.date != null && v.date!.isAfter(start) && v.date!.isBefore(end)
      ).toList();

      return HealthAnalyticsData(
        totalCattle: allCattle.length,
        totalHealthRecords: healthRecords.length,
        totalTreatments: treatments.length,
        totalVaccinations: vaccinations.length,
        healthRecords: healthRecords,
        treatments: treatments,
        vaccinations: vaccinations,
        cattle: allCattle,
        startDate: start,
        endDate: end,
      );
    } catch (e) {
      _logger.severe('Error generating health analytics: $e');
      rethrow;
    }
  }

  /// Get health condition distribution
  Map<String, int> getConditionDistribution(List<HealthRecordIsar> records) {
    final distribution = <String, int>{};
    for (final record in records) {
      final condition = record.condition?.toLowerCase() ?? 'unknown';
      distribution[condition] = (distribution[condition] ?? 0) + 1;
    }
    return distribution;
  }

  /// Get treatment effectiveness analysis
  Map<String, double> getTreatmentEffectiveness(List<HealthRecordIsar> records) {
    final treatmentOutcomes = <String, List<bool>>{};
    
    for (final record in records) {
      final treatment = record.treatment ?? 'unknown';
      final isResolved = record.isResolved ?? false;
      
      if (!treatmentOutcomes.containsKey(treatment)) {
        treatmentOutcomes[treatment] = [];
      }
      treatmentOutcomes[treatment]!.add(isResolved);
    }

    final effectiveness = <String, double>{};
    treatmentOutcomes.forEach((treatment, outcomes) {
      final successCount = outcomes.where((resolved) => resolved).length;
      effectiveness[treatment] = outcomes.isNotEmpty ? (successCount / outcomes.length) * 100 : 0;
    });

    return effectiveness;
  }

  /// Get monthly health trends
  Map<String, int> getMonthlyHealthTrends(List<HealthRecordIsar> records) {
    final trends = <String, int>{};
    
    for (final record in records) {
      if (record.date != null) {
        final monthKey = '${record.date!.year}-${record.date!.month.toString().padLeft(2, '0')}';
        trends[monthKey] = (trends[monthKey] ?? 0) + 1;
      }
    }
    
    return trends;
  }

  /// Get cost analysis
  HealthCostAnalysis getCostAnalysis(
    List<HealthRecordIsar> healthRecords,
    List<TreatmentIsar> treatments,
    List<VaccinationIsar> vaccinations,
  ) {
    double totalHealthCosts = 0;
    double totalTreatmentCosts = 0;
    double totalVaccinationCosts = 0;

    // Health record costs
    for (final record in healthRecords) {
      totalHealthCosts += record.cost ?? 0;
    }

    // Treatment costs
    for (final treatment in treatments) {
      final cost = double.tryParse(treatment.cost ?? '0') ?? 0;
      totalTreatmentCosts += cost;
    }

    // Vaccination costs
    for (final vaccination in vaccinations) {
      totalVaccinationCosts += vaccination.cost ?? 0;
    }

    return HealthCostAnalysis(
      totalHealthCosts: totalHealthCosts,
      totalTreatmentCosts: totalTreatmentCosts,
      totalVaccinationCosts: totalVaccinationCosts,
      totalCosts: totalHealthCosts + totalTreatmentCosts + totalVaccinationCosts,
    );
  }

  /// Get vaccination compliance analysis
  VaccinationComplianceData getVaccinationCompliance(
    List<CattleIsar> cattle,
    List<VaccinationIsar> vaccinations,
  ) {
    final now = DateTime.now();
    final sixMonthsAgo = DateTime(now.year, now.month - 6, now.day);
    
    int upToDateCattle = 0;
    int overdueCattle = 0;
    int dueSoonCattle = 0;

    for (final animal in cattle) {
      final cattleVaccinations = vaccinations.where((v) => v.cattleId == animal.businessId).toList();
      
      if (cattleVaccinations.isEmpty) {
        overdueCattle++;
        continue;
      }

      // Get most recent vaccination
      cattleVaccinations.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
      final latestVaccination = cattleVaccinations.first;

      if (latestVaccination.nextDueDate != null) {
        final dueDate = latestVaccination.nextDueDate!;
        final daysDifference = dueDate.difference(now).inDays;

        if (daysDifference < 0) {
          overdueCattle++;
        } else if (daysDifference <= 30) {
          dueSoonCattle++;
        } else {
          upToDateCattle++;
        }
      } else if (latestVaccination.date != null && latestVaccination.date!.isBefore(sixMonthsAgo)) {
        overdueCattle++;
      } else {
        upToDateCattle++;
      }
    }

    return VaccinationComplianceData(
      upToDate: upToDateCattle,
      dueSoon: dueSoonCattle,
      overdue: overdueCattle,
      total: cattle.length,
    );
  }

  /// Get health alerts that need attention
  Future<List<HealthAlert>> getHealthAlerts() async {
    final alerts = <HealthAlert>[];
    final now = DateTime.now();

    try {
      // Get all data
      final allHealthRecords = await _dbHelper.healthHandler.getAllHealthRecords();
      final allVaccinations = await _dbHelper.healthHandler.getAllVaccinations();

      // Check for follow-up appointments
      for (final record in allHealthRecords) {
        if (record.followUpRequired == true && record.followUpDate != null) {
          final daysDifference = record.followUpDate!.difference(now).inDays;
          
          if (daysDifference <= 0) {
            alerts.add(HealthAlert(
              type: HealthAlertType.followUpOverdue,
              title: 'Follow-up Overdue',
              message: 'Follow-up appointment was due on ${_formatDate(record.followUpDate!)}',
              cattleId: record.cattleId,
              priority: HealthAlertPriority.high,
              dueDate: record.followUpDate,
            ));
          } else if (daysDifference <= 3) {
            alerts.add(HealthAlert(
              type: HealthAlertType.followUpDue,
              title: 'Follow-up Due Soon',
              message: 'Follow-up appointment due on ${_formatDate(record.followUpDate!)}',
              cattleId: record.cattleId,
              priority: HealthAlertPriority.medium,
              dueDate: record.followUpDate,
            ));
          }
        }
      }

      // Check for vaccination due dates
      for (final vaccination in allVaccinations) {
        if (vaccination.nextDueDate != null) {
          final daysDifference = vaccination.nextDueDate!.difference(now).inDays;
          
          if (daysDifference <= 0) {
            alerts.add(HealthAlert(
              type: HealthAlertType.vaccinationOverdue,
              title: 'Vaccination Overdue',
              message: 'Vaccination was due on ${_formatDate(vaccination.nextDueDate!)}',
              cattleId: vaccination.cattleId,
              priority: HealthAlertPriority.high,
              dueDate: vaccination.nextDueDate,
            ));
          } else if (daysDifference <= 7) {
            alerts.add(HealthAlert(
              type: HealthAlertType.vaccinationDue,
              title: 'Vaccination Due Soon',
              message: 'Vaccination due on ${_formatDate(vaccination.nextDueDate!)}',
              cattleId: vaccination.cattleId,
              priority: HealthAlertPriority.medium,
              dueDate: vaccination.nextDueDate,
            ));
          }
        }
      }

      // Sort alerts by priority and date
      alerts.sort((a, b) {
        final priorityComparison = a.priority.index.compareTo(b.priority.index);
        if (priorityComparison != 0) return priorityComparison;
        
        if (a.dueDate != null && b.dueDate != null) {
          return a.dueDate!.compareTo(b.dueDate!);
        }
        return 0;
      });

    } catch (e) {
      _logger.severe('Error getting health alerts: $e');
    }

    return alerts;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Health analytics data container
class HealthAnalyticsData {
  final int totalCattle;
  final int totalHealthRecords;
  final int totalTreatments;
  final int totalVaccinations;
  final List<HealthRecordIsar> healthRecords;
  final List<TreatmentIsar> treatments;
  final List<VaccinationIsar> vaccinations;
  final List<CattleIsar> cattle;
  final DateTime startDate;
  final DateTime endDate;

  HealthAnalyticsData({
    required this.totalCattle,
    required this.totalHealthRecords,
    required this.totalTreatments,
    required this.totalVaccinations,
    required this.healthRecords,
    required this.treatments,
    required this.vaccinations,
    required this.cattle,
    required this.startDate,
    required this.endDate,
  });
}

/// Health cost analysis data
class HealthCostAnalysis {
  final double totalHealthCosts;
  final double totalTreatmentCosts;
  final double totalVaccinationCosts;
  final double totalCosts;

  HealthCostAnalysis({
    required this.totalHealthCosts,
    required this.totalTreatmentCosts,
    required this.totalVaccinationCosts,
    required this.totalCosts,
  });
}

/// Vaccination compliance data
class VaccinationComplianceData {
  final int upToDate;
  final int dueSoon;
  final int overdue;
  final int total;

  VaccinationComplianceData({
    required this.upToDate,
    required this.dueSoon,
    required this.overdue,
    required this.total,
  });

  double get compliancePercentage => total > 0 ? (upToDate / total) * 100 : 0;
}

/// Health alert types
enum HealthAlertType {
  followUpDue,
  followUpOverdue,
  vaccinationDue,
  vaccinationOverdue,
}

/// Health alert priorities
enum HealthAlertPriority {
  high,
  medium,
  low,
}

/// Health alert data
class HealthAlert {
  final HealthAlertType type;
  final String title;
  final String message;
  final String? cattleId;
  final HealthAlertPriority priority;
  final DateTime? dueDate;

  HealthAlert({
    required this.type,
    required this.title,
    required this.message,
    this.cattleId,
    required this.priority,
    this.dueDate,
  });
}
