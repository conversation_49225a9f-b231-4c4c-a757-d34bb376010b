import 'dart:async';
import 'dart:isolate';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'health_alert_service.dart';

/// Service for scheduling health-related tasks and alerts
class HealthSchedulerService {
  static final Logger _logger = Logger('HealthSchedulerService');
  static HealthSchedulerService? _instance;
  static HealthSchedulerService get instance => _instance ??= HealthSchedulerService._internal();

  Timer? _dailyTimer;
  Timer? _hourlyTimer;
  late final HealthAlertService _alertService;

  HealthSchedulerService._internal() {
    _alertService = HealthAlertService();
  }

  /// Initialize the scheduler service
  Future<void> initialize() async {
    try {
      _logger.info('Initializing Health Scheduler Service');
      
      // Check if this is the first run today
      await _checkAndRunDailyTasks();
      
      // Schedule daily tasks (run at 8 AM every day)
      _scheduleDailyTasks();
      
      // Schedule hourly cleanup tasks
      _scheduleHourlyTasks();
      
      _logger.info('Health Scheduler Service initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing Health Scheduler Service: $e');
    }
  }

  /// Schedule daily health tasks
  void _scheduleDailyTasks() {
    // Cancel existing timer
    _dailyTimer?.cancel();

    // Calculate time until next 8 AM
    final now = DateTime.now();
    var nextRun = DateTime(now.year, now.month, now.day, 8, 0, 0);
    
    // If it's already past 8 AM today, schedule for tomorrow
    if (nextRun.isBefore(now)) {
      nextRun = nextRun.add(const Duration(days: 1));
    }

    final timeUntilNextRun = nextRun.difference(now);
    
    _logger.info('Scheduling daily health tasks to run at $nextRun (in ${timeUntilNextRun.inHours} hours)');

    _dailyTimer = Timer(timeUntilNextRun, () {
      _runDailyTasks();
      // Reschedule for the next day
      _scheduleDailyTasks();
    });
  }

  /// Schedule hourly cleanup tasks
  void _scheduleHourlyTasks() {
    // Cancel existing timer
    _hourlyTimer?.cancel();

    // Run every hour
    _hourlyTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      _runHourlyTasks();
    });

    _logger.info('Scheduled hourly health tasks');
  }

  /// Check if daily tasks need to be run and run them if necessary
  Future<void> _checkAndRunDailyTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastRunDate = prefs.getString('health_daily_tasks_last_run');
      final today = DateTime.now().toIso8601String().substring(0, 10); // YYYY-MM-DD format

      if (lastRunDate != today) {
        _logger.info('Running daily health tasks (last run: $lastRunDate, today: $today)');
        await _runDailyTasks();
        await prefs.setString('health_daily_tasks_last_run', today);
      } else {
        _logger.info('Daily health tasks already run today');
      }
    } catch (e) {
      _logger.severe('Error checking daily tasks: $e');
    }
  }

  /// Run daily health tasks
  Future<void> _runDailyTasks() async {
    try {
      _logger.info('Running daily health tasks');
      
      // Generate health alerts
      await _alertService.generateAlerts();
      
      // Clean up old alerts (run weekly - check if it's Monday)
      final now = DateTime.now();
      if (now.weekday == DateTime.monday) {
        await _alertService.cleanupOldAlerts();
      }
      
      _logger.info('Daily health tasks completed');
    } catch (e) {
      _logger.severe('Error running daily health tasks: $e');
    }
  }

  /// Run hourly health tasks
  Future<void> _runHourlyTasks() async {
    try {
      _logger.info('Running hourly health tasks');
      
      // Currently no hourly tasks, but this is where they would go
      // For example: checking for urgent alerts, updating health scores, etc.
      
      _logger.info('Hourly health tasks completed');
    } catch (e) {
      _logger.severe('Error running hourly health tasks: $e');
    }
  }

  /// Manually trigger alert generation (for testing or immediate updates)
  Future<void> generateAlertsNow() async {
    try {
      _logger.info('Manually generating health alerts');
      await _alertService.generateAlerts();
      _logger.info('Manual health alert generation completed');
    } catch (e) {
      _logger.severe('Error manually generating health alerts: $e');
      rethrow;
    }
  }

  /// Get the next scheduled run time for daily tasks
  DateTime? getNextDailyRunTime() {
    if (_dailyTimer == null) return null;
    
    final now = DateTime.now();
    var nextRun = DateTime(now.year, now.month, now.day, 8, 0, 0);
    
    if (nextRun.isBefore(now)) {
      nextRun = nextRun.add(const Duration(days: 1));
    }
    
    return nextRun;
  }

  /// Check if the scheduler is running
  bool get isRunning => _dailyTimer != null && _hourlyTimer != null;

  /// Get scheduler status information
  Map<String, dynamic> getStatus() {
    return {
      'isRunning': isRunning,
      'nextDailyRun': getNextDailyRunTime()?.toIso8601String(),
      'dailyTimerActive': _dailyTimer?.isActive ?? false,
      'hourlyTimerActive': _hourlyTimer?.isActive ?? false,
    };
  }

  /// Stop the scheduler
  void stop() {
    _logger.info('Stopping Health Scheduler Service');
    _dailyTimer?.cancel();
    _hourlyTimer?.cancel();
    _dailyTimer = null;
    _hourlyTimer = null;
  }

  /// Restart the scheduler
  Future<void> restart() async {
    _logger.info('Restarting Health Scheduler Service');
    stop();
    await initialize();
  }

  /// Run health tasks in background isolate (for heavy operations)
  static Future<void> _runInBackground(String taskType) async {
    try {
      final receivePort = ReceivePort();
      
      await Isolate.spawn(_backgroundTaskEntry, {
        'sendPort': receivePort.sendPort,
        'taskType': taskType,
      });

      // Wait for completion
      await receivePort.first;
      receivePort.close();
    } catch (e) {
      Logger('HealthSchedulerService').severe('Error running background task: $e');
    }
  }

  /// Entry point for background isolate
  static void _backgroundTaskEntry(Map<String, dynamic> params) async {
    try {
      final sendPort = params['sendPort'] as SendPort;
      final taskType = params['taskType'] as String;

      // Initialize services in the isolate
      final alertService = HealthAlertService();

      switch (taskType) {
        case 'generateAlerts':
          await alertService.generateAlerts();
          break;
        case 'cleanupAlerts':
          await alertService.cleanupOldAlerts();
          break;
        default:
          throw Exception('Unknown task type: $taskType');
      }

      // Signal completion
      sendPort.send('completed');
    } catch (e) {
      Logger('HealthSchedulerService').severe('Error in background task: $e');
    }
  }

  /// Schedule a one-time task to run at a specific time
  Timer scheduleOneTimeTask(DateTime runTime, Future<void> Function() task) {
    final now = DateTime.now();
    final delay = runTime.difference(now);

    if (delay.isNegative) {
      throw ArgumentError('Cannot schedule task in the past');
    }

    _logger.info('Scheduling one-time task to run at $runTime (in ${delay.inMinutes} minutes)');

    return Timer(delay, () async {
      try {
        await task();
        _logger.info('One-time task completed successfully');
      } catch (e) {
        _logger.severe('Error running one-time task: $e');
      }
    });
  }

  /// Schedule a recurring task
  Timer scheduleRecurringTask(Duration interval, Future<void> Function() task) {
    _logger.info('Scheduling recurring task with interval: ${interval.inMinutes} minutes');

    return Timer.periodic(interval, (timer) async {
      try {
        await task();
        _logger.info('Recurring task completed successfully');
      } catch (e) {
        _logger.severe('Error running recurring task: $e');
      }
    });
  }

  /// Get health alert statistics
  Future<Map<String, int>> getAlertStatistics() async {
    try {
      final allAlerts = await _alertService.getAllHealthAlerts();
      final unreadCount = await _alertService.getUnreadHealthAlertsCount();

      final highPriorityCount = allAlerts.where((alert) => alert.priority == 'high').length;
      final mediumPriorityCount = allAlerts.where((alert) => alert.priority == 'medium').length;
      final lowPriorityCount = allAlerts.where((alert) => alert.priority == 'low').length;

      return {
        'total': allAlerts.length,
        'unread': unreadCount,
        'high': highPriorityCount,
        'medium': mediumPriorityCount,
        'low': lowPriorityCount,
      };
    } catch (e) {
      _logger.severe('Error getting alert statistics: $e');
      return {
        'total': 0,
        'unread': 0,
        'high': 0,
        'medium': 0,
        'low': 0,
      };
    }
  }

  /// Dispose of the service
  void dispose() {
    _logger.info('Disposing Health Scheduler Service');
    stop();
  }
}
