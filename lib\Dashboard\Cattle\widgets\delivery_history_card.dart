import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';

class DeliveryHistoryCard extends StatelessWidget {
  final List<Map<String, dynamic>> records;
  final String title;
  final String emptyMessage;
  final Function(Map<String, dynamic>)? onEdit;
  final Function(Map<String, dynamic>)? onDelete;
  final Function(Map<String, dynamic>)? onCattleTap; // Add cattle tap callback

  const DeliveryHistoryCard({
    super.key,
    required this.records,
    this.title = 'Delivery History',
    this.emptyMessage = 'No delivery records found',
    this.onEdit,
    this.onDelete,
    this.onCattleTap, // Add cattle tap callback
  });

  @override
  Widget build(BuildContext context) {
    // If no records, show empty state directly to match original design
    if (records.isEmpty) {
      return Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.only(bottom: 16),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7D32).withAlpha(26), // 0.1 * 255 = 26
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor:
                        const Color(0xFF2E7D32).withAlpha(51), // 0.2 * 255 = 51
                    child: const Icon(
                      Icons.history,
                      color: Color(0xFF2E7D32),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7D32).withAlpha(26), // 0.1 * 255 = 26
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.child_care_outlined,
                        size: 48,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      emptyMessage,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add a delivery record to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // With records, show the full list
    return Card(
      elevation: 2,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32).withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor:
                      const Color(0xFF2E7D32).withAlpha(51), // 0.2 * 255 = 51
                  child: const Icon(
                    Icons.history, // Using history icon as in original
                    color: Color(0xFF2E7D32),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),

          // History List
          Flexible(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: records.length,
                itemBuilder: (context, index) {
                  // Add divider between items
                  if (index > 0) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Divider(height: 32),
                        _buildDeliveryRecord(context, records[index]),
                      ],
                    );
                  }
                  return _buildDeliveryRecord(context, records[index]);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryRecord(
      BuildContext context, Map<String, dynamic> record) {
    // Safely parse the delivery date - check both 'deliveryDate' and 'date' fields
    DateTime deliveryDate;
    try {
      final dateStr = record['deliveryDate']?.toString() ??
                     record['date']?.toString() ??
                     DateTime.now().toIso8601String();
      deliveryDate = DateTime.parse(dateStr);
    } catch (e) {
      deliveryDate = DateTime.now();
    }

    final deliveryType = record['deliveryType'] ?? record['type'] ?? 'Unknown';
    final numberOfCalves = record['numberOfCalves']?.toString() ?? '1';
    final calfDetails = record['calfDetails'] as List<dynamic>? ?? [];
    final notes = record['notes']?.toString() ?? '';
    final cattleId = record['cattleId']?.toString() ?? '';
    final cattleName = record['cattleName']?.toString() ?? '';

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Record Header - make entire header tappable
        GestureDetector(
          onTap: onCattleTap != null ? () => onCattleTap!(record) : null,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.teal
                  .withAlpha(51), // 0.2 * 255 = 51 (Matching original teal color)
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor:
                            Colors.teal.withAlpha(70), // 0.27 * 255 = 70 (standardized)
                        child: const Icon(
                          Icons
                              .baby_changing_station, // Keep this icon for individual records
                          color: Colors.teal,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('MMMM dd, yyyy').format(deliveryDate),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight
                                    .bold, // Changed to bold as in original
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${cattleName.isNotEmpty ? cattleName : 'Unknown'} (${cattleId.isNotEmpty ? cattleId : 'Unknown'})',
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: Colors.teal, // Standardized to status color
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // More options menu (edit/delete)
                if (onEdit != null || onDelete != null)
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) {
                      if (value == 'edit' && onEdit != null) {
                        onEdit!(record);
                      } else if (value == 'delete' && onDelete != null) {
                        onDelete!(record);
                      }
                    },
                    itemBuilder: (context) => [
                      if (onEdit != null)
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                      if (onDelete != null)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
              ],
            ),
          ),
        ),

        // Record Details
        Padding(
          padding: const EdgeInsets.all(16), // Match original padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Delivery Type
              InfoRow(
                icon: Icons.medical_services,
                label: 'Delivery Type',
                value: deliveryType,
                color: Colors.teal,
              ),
              const SizedBox(height: 8),

              // Number of Calves
              InfoRow(
                icon: Icons.child_care,
                label: 'Number of Calves',
                value: numberOfCalves,
                color: Colors.indigo,
              ),

              // Calf Details
              if (calfDetails.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                const Text(
                  'Calf Details:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Color(0xFF7B1FA2), // Purple - unique color not used elsewhere
                  ),
                ),
                const SizedBox(height: 8),
                ...calfDetails.map((calf) {
                  final calfName = calf['name'] ?? 'Unnamed';
                  final calfTagId = calf['tagId'] ?? 'No Tag';
                  final calfGender = calf['gender'] ?? 'Unknown';
                  final isFemaleCalf = calfGender.toLowerCase() == 'female';

                  return Column(
                    children: [
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Label section with icon
                            Expanded(
                              flex: 1,
                              child: Row(
                                children: [
                                  Icon(
                                    isFemaleCalf ? Icons.female : Icons.male,
                                    size: 20,
                                    color: isFemaleCalf ? const Color(0xFFE91E63) : const Color(0xFF1565C0),
                                  ),
                                  const SizedBox(width: 8),
                                  const Expanded(
                                    child: Text(
                                      'Gender',
                                      style: TextStyle(
                                        color: Colors.black87,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Value section with custom styling
                            Expanded(
                              flex: 1,
                              child: Align(
                                alignment: Alignment.centerRight,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: (isFemaleCalf ? const Color(0xFFE91E63) : const Color(0xFF1565C0)).withAlpha(40),
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: (isFemaleCalf ? const Color(0xFFE91E63) : const Color(0xFF1565C0)).withAlpha(100),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    calfGender,
                                    style: TextStyle(
                                      color: isFemaleCalf ? const Color(0xFFE91E63) : const Color(0xFF1565C0),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 13,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      InfoRow(
                        icon: Icons.label,
                        label: 'Name',
                        value: calfName,
                        color: Colors.deepPurple,
                      ),
                      const SizedBox(height: 8),
                      InfoRow(
                        icon: Icons.tag,
                        label: 'Tag ID',
                        value: calfTagId,
                        color: Colors.green,
                      ),
                      const SizedBox(height: 16),
                    ],
                  );
                }).toList(),
              ],

              // Notes section
              if (notes.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                InfoRow(
                  icon: Icons.notes,
                  label: 'Notes',
                  value: notes,
                  color: Colors.brown, // Match original color
                  isMultiline: true,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
