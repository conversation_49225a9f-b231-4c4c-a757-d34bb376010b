import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';
import 'package:collection/collection.dart';

import '../models/weight_record.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Consolidated handler for all Weight module database operations
class WeightHandler {
  static final Logger _logger = Logger('WeightHandler');
  final IsarService _isarService;

  // Singleton instance
  static final WeightHandler _instance = WeightHandler._internal();
  static WeightHandler get instance => _instance;

  // Private constructor
  WeightHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  /// Get all weight records
  Future<List<WeightRecord>> getAllWeightRecords() async {
    try {
      return await _isar.weightRecords.where().sortByDateDesc().findAll();
    } catch (e) {
      _logger.severe('Error getting all weight records: $e');
      throw DatabaseException(
          'Failed to retrieve weight records', e.toString());
    }
  }

  /// Get weight records for a specific cattle
  Future<List<WeightRecord>> getWeightRecordsForCattle(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.weightRecords
          .filter()
          .cattleIdEqualTo(cattleId)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting weight records for cattle $cattleId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve weight records', e.toString());
    }
  }

  /// Get weight records for a date range
  Future<List<WeightRecord>> getWeightRecordsForDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      return await _isar.weightRecords
          .filter()
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting weight records for date range: $e');
      throw DatabaseException(
          'Failed to retrieve weight records', e.toString());
    }
  }

  /// Add a new weight record
  Future<void> addWeightRecord(WeightRecord record) async {
    try {
      await _validateWeightRecord(record, isNew: true);

      await _isar.writeTxn(() async {
        await _isar.weightRecords.put(record);
      });

      _logger.info('Successfully added weight record: ${record.recordId}');
    } catch (e) {
      _logger.severe('Error adding weight record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add weight record', e.toString());
    }
  }

  /// Update an existing weight record
  Future<void> updateWeightRecord(WeightRecord record) async {
    try {
      await _validateWeightRecord(record, isNew: false);

      await _isar.writeTxn(() async {
        await _isar.weightRecords.put(record);
      });

      _logger.info('Successfully updated weight record: ${record.recordId}');
    } catch (e) {
      _logger.severe('Error updating weight record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to update weight record', e.toString());
    }
  }

  /// Delete a weight record
  Future<void> deleteWeightRecord(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Weight record ID is required');
      }

      await _isar.writeTxn(() async {
        final record = await _isar.weightRecords
            .filter()
            .recordIdEqualTo(recordId)
            .findFirst();

        if (record == null) {
          throw RecordNotFoundException('Weight record not found: $recordId');
        }

        await _isar.weightRecords.delete(record.id);
      });

      _logger.info('Successfully deleted weight record: $recordId');
    } catch (e) {
      _logger.severe('Error deleting weight record: $recordId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete weight record', e.toString());
    }
  }

  /// Calculate average weight for a cattle over a date range
  Future<double?> calculateAverageWeight(
      String cattleId, DateTime startDate, DateTime endDate) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final records = await _isar.weightRecords
          .filter()
          .cattleIdEqualTo(cattleId)
          .and()
          .dateBetween(startDate, endDate)
          .findAll();

      if (records.isEmpty) {
        return null;
      }

      final totalWeight = records.fold<double>(
        0,
        (sum, record) => sum + record.weight,
      );

      return totalWeight / records.length;
    } catch (e) {
      _logger
          .severe('Error calculating average weight for cattle $cattleId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to calculate average weight', e.toString());
    }
  }

  /// Calculate weight gain/loss between two dates for a cattle
  Future<double?> calculateWeightChange(
      String cattleId, DateTime startDate, DateTime endDate) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final startRecord = await _isar.weightRecords
          .filter()
          .cattleIdEqualTo(cattleId)
          .and()
          .dateGreaterThan(startDate)
          .sortByDate()
          .findFirst();

      final endRecord = await _isar.weightRecords
          .filter()
          .cattleIdEqualTo(cattleId)
          .and()
          .dateLessThan(endDate)
          .sortByDateDesc()
          .findFirst();

      if (startRecord == null || endRecord == null) {
        return null;
      }

      return endRecord.weight - startRecord.weight;
    } catch (e) {
      _logger
          .severe('Error calculating weight change for cattle $cattleId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to calculate weight change', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate weight record
  Future<void> _validateWeightRecord(WeightRecord record,
      {required bool isNew}) async {
    if (record.cattleId.isEmpty) {
      throw ValidationException('Cattle ID is required');
    }

    if (record.weight <= 0) {
      throw ValidationException('Valid weight value is required');
    }

    // Validate cattle exists
    final allCattle = await _isarService.cattleIsars.where().findAll();
    final cattle =
        allCattle.firstWhereOrNull((c) => c.tagId == record.cattleId);

    if (cattle == null) {
      throw ValidationException('Invalid cattle ID');
    }

    // No need to set createdAt/updatedAt as those aren't in the model
  }
}
