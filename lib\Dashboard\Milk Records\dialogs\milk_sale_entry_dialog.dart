import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../models/milk_sale_isar.dart';
import '../services/milk_sales_service.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import '../services/milk_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../Farm Setup/models/milk_settings_isar.dart';
import '../../../utils/message_utils.dart';

class MilkSaleEntryDialog extends StatefulWidget {
  final DateTime selectedDate;
  final double availableMilk;

  const MilkSaleEntryDialog({
    super.key,
    required this.selectedDate,
    required this.availableMilk,
  });

  @override
  State<MilkSaleEntryDialog> createState() => _MilkSaleEntryDialogState();
}

class _MilkSaleEntryDialogState extends State<MilkSaleEntryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _milkSalesService = MilkSalesService();
  final _milkService = MilkService();
  final _farmSetupHandler = FarmSetupHandler.instance;

  final _quantityController = TextEditingController();
  final _rateController = TextEditingController();
  final _calfUsageController = TextEditingController();
  final _homeUsageController = TextEditingController();
  final _notesController = TextEditingController();
  final _totalProductionController = TextEditingController();
  bool _isPaid = true;
  double _totalAmount = 0;
  DateTime _selectedDate = DateTime.now();
  double _availableMilk = 0;

  // Currency settings
  String _currencySymbol = '₹';
  bool _symbolBeforeAmount = true;

  // Milk settings
  String _milkUnit = 'liters';
  double _defaultRate = 60.0;
  MilkSettingsIsar? _milkSettings;

  // Message display state
  String? _messageText;
  Color? _messageColor;
  IconData? _messageIcon;
  bool _showMessage = false;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    _availableMilk = widget.availableMilk;
    _totalProductionController.text = '${_availableMilk.toStringAsFixed(1)} ${_getUnitAbbreviation()}';
    _loadCurrencySettings();
    _loadMilkSettings();

    // Load fresh data for the selected date to ensure accuracy
    _loadAvailableMilk(_selectedDate);
    _updateTotalAmount();

    // Add listeners with debouncing to avoid excessive rebuilds
    _quantityController.addListener(_onQuantityChanged);
    _calfUsageController.addListener(_onUsageChanged);
    _homeUsageController.addListener(_onUsageChanged);
    _rateController.addListener(_updateTotalAmount);
  }

  // Debounced listeners to improve performance
  void _onQuantityChanged() {
    _updateTotalAmount();
  }

  void _onUsageChanged() {
    _autoFillQuantityOnUsageChange();
  }

  // Message display methods
  void _showErrorMessage(String message) {
    setState(() {
      _messageText = message;
      _messageColor = Colors.red;
      _messageIcon = Icons.error;
      _showMessage = true;
    });
    _hideMessageAfterDelay();
  }

  void _showSuccessMessage(String message) {
    setState(() {
      _messageText = message;
      _messageColor = Colors.green;
      _messageIcon = Icons.check_circle;
      _showMessage = true;
    });
    _hideMessageAfterDelay();
  }



  void _hideMessage() {
    setState(() {
      _showMessage = false;
    });
  }

  void _hideMessageAfterDelay() {
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        _hideMessage();
      }
    });
  }

  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  Future<void> _loadMilkSettings() async {
    try {
      final milkSettings = await _farmSetupHandler.getMilkSettings();
      if (mounted) {
        setState(() {
          _milkSettings = milkSettings;
          _milkUnit = milkSettings.unit ?? 'liters';
          _defaultRate = milkSettings.regularRate ?? 60.0;
          // Set the default rate in the controller
          _rateController.text = _defaultRate.toStringAsFixed(1);
        });
      }
    } catch (e) {
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _milkUnit = 'liters';
          _defaultRate = 60.0;
          _rateController.text = _defaultRate.toStringAsFixed(1);
        });
      }
    }
  }

  Future<void> _loadAvailableMilk(DateTime date) async {
    try {
      // Get milk records for the selected date
      final records = await _milkService.getMilkRecordsForDate(date);

      // Calculate total production
      double totalProduction = records.fold<double>(
          0.0,
          (sum, record) =>
              sum + (record.morningAmount ?? 0) + (record.eveningAmount ?? 0));

      // Get existing sales for the selected date
      final existingSales = await _milkSalesService.getMilkSalesForDate(date);

      // Calculate total already sold
      double totalSold = existingSales.fold<double>(0.0, (sum, sale) => sum + sale.quantity);

      // Calculate available milk (total production - already sold)
      double availableMilk = totalProduction - totalSold;

      if (mounted) {
        setState(() {
          _availableMilk = availableMilk.clamp(0.0, double.infinity);
          _totalProductionController.text = '${_availableMilk.toStringAsFixed(1)} ${_getUnitAbbreviation()}';
          _autoFillQuantityOnUsageChange(); // Auto-fill quantity when data loads
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('Error loading milk data: $e');
      }
    }
  }

  void _updateTotalAmount() {
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    final rate = double.tryParse(_rateController.text) ?? 0;
    final newTotal = quantity * rate;

    // Only update if the value actually changed to avoid unnecessary rebuilds
    if (_totalAmount != newTotal) {
      setState(() {
        _totalAmount = newTotal;
      });
    }
  }



  // Auto-fill quantity when usage changes
  void _autoFillQuantityOnUsageChange() {
    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0;
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0;
    final availableForSale = _availableMilk - calfUsage - homeUsage;

    // Auto-fill with available quantity for sale
    if (availableForSale >= 0) {
      _quantityController.text = availableForSale.toStringAsFixed(1);
    } else {
      _quantityController.text = '0.0';
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF2E7D32),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      await _loadAvailableMilk(_selectedDate);
    }
  }

  Future<void> _saveMilkSale() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final sale = MilkSaleIsar()
        ..saleId = MilkSaleIsar.generateSaleId() // Generate unique sale ID
        ..date = _selectedDate
        ..buyer = "Default" // Changed from buyerName to buyer
        ..quantity = double.parse(_quantityController.text) // Changed from quantitySold to quantity
        ..price = double.parse(_rateController.text) // Changed from ratePerLiter to price
        ..total = _totalAmount
        ..paymentStatus = _isPaid ? 'Paid' : 'Pending' // Store payment status as string
        ..notes = _notesController.text.trim();

      await _milkSalesService.addMilkSale(sale);

      if (mounted) {
        _showSuccessMessage(MilkMessageUtils.milkSaleRecorded());
        // Close dialog after a short delay to show success message
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('Error saving milk sale: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final dialogWidth = isTablet ? 600.0 : screenWidth * 0.95;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
          maxWidth: dialogWidth,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFF2E7D32),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.point_of_sale,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Milk Sale Entry',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: KeyboardDismisser(
                gestures: const [
                  GestureType.onTap,
                  GestureType.onPanUpdateDownDirection
                ],
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Message display at top
                        if (_showMessage && _messageText != null)
                          Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: _messageColor?.withValues(alpha: 0.1),
                              border: Border.all(color: _messageColor ?? Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  _messageIcon,
                                  color: _messageColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _messageText!,
                                    style: TextStyle(
                                      color: _messageColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                IconButton(
                                  icon: Icon(Icons.close, color: _messageColor, size: 18),
                                  onPressed: _hideMessage,
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                              ],
                            ),
                          ),

                        // Date Picker
                        InkWell(
                          onTap: () => _selectDate(context),
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: 'Date',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                              prefixIcon: const Icon(
                                Icons.calendar_today,
                                color: Colors.indigo,
                              ),
                            ),
                            child: Text(
                              DateFormat('dd MMM yyyy').format(_selectedDate),
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Total Production field (read-only)
                        TextFormField(
                          controller: _totalProductionController,
                          decoration: InputDecoration(
                            labelText: 'Total Production',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                            prefixIcon: const Icon(
                              Icons.inventory,
                              color: Colors.teal,
                            ),
                            filled: true,
                            fillColor: Colors.blue.shade50,
                          ),
                          readOnly: true,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Usage inputs
                        isTablet
                          ? Row(
                              children: [
                                Expanded(
                                  child: _buildStandardInput(
                                    label: 'Calves Usage (${_getUnitAbbreviation()})',
                                    controller: _calfUsageController,
                                    hint: '0.0',
                                    validator: _validateUsage,
                                    prefixIcon: Icons.pets,
                                    prefixIconColor: Colors.brown,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildStandardInput(
                                    label: 'Home Usage (${_getUnitAbbreviation()})',
                                    controller: _homeUsageController,
                                    hint: '0.0',
                                    validator: _validateUsage,
                                    prefixIcon: Icons.home,
                                    prefixIconColor: Colors.purple,
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              children: [
                                _buildStandardInput(
                                  label: 'Calves Usage (${_getUnitAbbreviation()})',
                                  controller: _calfUsageController,
                                  hint: '0.0',
                                  validator: _validateUsage,
                                  prefixIcon: Icons.pets,
                                  prefixIconColor: Colors.brown,
                                ),
                                const SizedBox(height: 16),
                                _buildStandardInput(
                                  label: 'Home Usage (${_getUnitAbbreviation()})',
                                  controller: _homeUsageController,
                                  hint: '0.0',
                                  validator: _validateUsage,
                                  prefixIcon: Icons.home,
                                  prefixIconColor: Colors.purple,
                                ),
                              ],
                            ),
                        const SizedBox(height: 16),

                        // Sale details
                        isTablet
                          ? Row(
                              children: [
                                Expanded(
                                  child: _buildStandardInput(
                                    label: 'Quantity (${_getUnitAbbreviation()})',
                                    controller: _quantityController,
                                    hint: '0.0',
                                    validator: _validateQuantity,
                                    prefixIcon: Icons.water_drop,
                                    prefixIconColor: Colors.cyan,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildStandardInput(
                                    label: 'Rate ($_currencySymbol/${_getUnitAbbreviation()})',
                                    controller: _rateController,
                                    hint: _defaultRate.toStringAsFixed(1),
                                    prefix: '$_currencySymbol ',
                                    validator: _validateRate,
                                    onActionPressed: _showRateOptions,
                                    actionIcon: Icons.more_vert,
                                    prefixIcon: Icons.attach_money,
                                    prefixIconColor: Colors.green,
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              children: [
                                _buildStandardInput(
                                  label: 'Quantity (${_getUnitAbbreviation()})',
                                  controller: _quantityController,
                                  hint: '0.0',
                                  validator: _validateQuantity,
                                  prefixIcon: Icons.water_drop,
                                  prefixIconColor: Colors.cyan,
                                ),
                                const SizedBox(height: 16),
                                _buildStandardInput(
                                  label: 'Rate ($_currencySymbol/${_getUnitAbbreviation()})',
                                  controller: _rateController,
                                  hint: _defaultRate.toStringAsFixed(1),
                                  prefix: '$_currencySymbol ',
                                  validator: _validateRate,
                                  onActionPressed: _showRateOptions,
                                  actionIcon: Icons.more_vert,
                                  prefixIcon: Icons.attach_money,
                                  prefixIconColor: Colors.green,
                                ),
                              ],
                            ),
                        const SizedBox(height: 16),

                        // Total amount
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Total Amount:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                _formatCurrency(_totalAmount),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2E7D32),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Payment status
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Payment Status',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            isTablet
                              ? Row(
                                  children: [
                                    Expanded(
                                      child: RadioListTile<bool>(
                                        title: const Text('Paid'),
                                        value: true,
                                        groupValue: _isPaid,
                                        onChanged: (value) => setState(() => _isPaid = value!),
                                        activeColor: const Color(0xFF2E7D32),
                                        contentPadding: EdgeInsets.zero,
                                      ),
                                    ),
                                    Expanded(
                                      child: RadioListTile<bool>(
                                        title: const Text('Pending'),
                                        value: false,
                                        groupValue: _isPaid,
                                        onChanged: (value) => setState(() => _isPaid = value!),
                                        activeColor: const Color(0xFF2E7D32),
                                        contentPadding: EdgeInsets.zero,
                                      ),
                                    ),
                                  ],
                                )
                              : Column(
                                  children: [
                                    RadioListTile<bool>(
                                      title: const Text('Paid'),
                                      value: true,
                                      groupValue: _isPaid,
                                      onChanged: (value) => setState(() => _isPaid = value!),
                                      activeColor: const Color(0xFF2E7D32),
                                      contentPadding: EdgeInsets.zero,
                                    ),
                                    RadioListTile<bool>(
                                      title: const Text('Pending'),
                                      value: false,
                                      groupValue: _isPaid,
                                      onChanged: (value) => setState(() => _isPaid = value!),
                                      activeColor: const Color(0xFF2E7D32),
                                      contentPadding: EdgeInsets.zero,
                                    ),
                                  ],
                                ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Notes (optional)
                        TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'Notes (Optional)',
                            hintText: 'Enter any additional information...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                            prefixIcon: const Icon(
                              Icons.note_add,
                              color: Colors.deepPurple,
                            ),
                          ),
                          maxLines: 3,
                        ),
                        const SizedBox(height: 24),

                        // Submit button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _saveMilkSale,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7D32),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text(
                              'SAVE MILK SALE',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildStandardInput({
    required String label,
    required TextEditingController controller,
    required String hint,
    String? prefix,
    required String? Function(String?) validator,
    VoidCallback? onActionPressed,
    IconData? actionIcon,
    IconData? prefixIcon,
    Color? prefixIconColor,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: prefix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        prefixIcon: prefixIcon != null
          ? Icon(prefixIcon, color: prefixIconColor ?? Colors.blueGrey)
          : null,
        suffixIcon: (onActionPressed != null && actionIcon != null)
          ? IconButton(
              icon: Icon(actionIcon, color: Colors.deepOrange),
              onPressed: onActionPressed,
            )
          : null,
        isDense: false,
      ),
      style: const TextStyle(fontSize: 16),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      validator: validator,
    );
  }

  String? _validateUsage(String? value) {
    if (value == null || value.isEmpty) return null;
    final usage = double.tryParse(value);
    if (usage == null) return 'Invalid number';
    if (usage < 0) return 'Cannot be negative';
    if (usage > _availableMilk) return 'Exceeds available milk';
    return null;
  }

  String? _validateQuantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }
    final quantity = double.tryParse(value);
    if (quantity == null) return 'Invalid number';
    if (quantity <= 0) return 'Must be greater than 0';

    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0;
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0;
    final availableForSale = _availableMilk - calfUsage - homeUsage;

    if (quantity > availableForSale) {
      return 'Exceeds available (${availableForSale.toStringAsFixed(1)}${_getUnitAbbreviation()})';
    }
    return null;
  }

  String? _validateRate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }
    final rate = double.tryParse(value);
    if (rate == null) return 'Invalid number';
    if (rate <= 0) return 'Must be greater than 0';
    if (rate > 1000) return 'Rate seems too high';
    return null;
  }

  // Format currency based on user settings
  String _formatCurrency(double amount) {
    return _symbolBeforeAmount
        ? '$_currencySymbol${amount.toStringAsFixed(2)}'
        : '${amount.toStringAsFixed(2)}$_currencySymbol';
  }

  // Get unit abbreviation based on milk settings
  String _getUnitAbbreviation() {
    switch (_milkUnit.toLowerCase()) {
      case 'liters':
      case 'litres':
        return 'L';
      case 'gallons':
        return 'gal';
      case 'quarts':
        return 'qt';
      case 'pints':
        return 'pt';
      default:
        return 'L'; // Default to liters
    }
  }



  // Show rate options from milk settings
  void _showRateOptions() {
    if (_milkSettings == null) return;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select Rate Type',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              if (_milkSettings!.regularRate != null)
                ListTile(
                  leading: const Icon(Icons.local_offer, color: Colors.green),
                  title: const Text('Regular Rate'),
                  subtitle: Text(_formatCurrency(_milkSettings!.regularRate!)),
                  onTap: () {
                    _rateController.text = _milkSettings!.regularRate!.toStringAsFixed(1);
                    Navigator.pop(context);
                  },
                ),
              if (_milkSettings!.premiumRate != null)
                ListTile(
                  leading: const Icon(Icons.star, color: Colors.purple),
                  title: const Text('Premium Rate'),
                  subtitle: Text(_formatCurrency(_milkSettings!.premiumRate!)),
                  onTap: () {
                    _rateController.text = _milkSettings!.premiumRate!.toStringAsFixed(1);
                    Navigator.pop(context);
                  },
                ),
              if (_milkSettings!.bulkRate != null)
                ListTile(
                  leading: const Icon(Icons.inventory, color: Colors.blue),
                  title: const Text('Bulk Rate'),
                  subtitle: Text(_formatCurrency(_milkSettings!.bulkRate!)),
                  onTap: () {
                    _rateController.text = _milkSettings!.bulkRate!.toStringAsFixed(1);
                    Navigator.pop(context);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _rateController.dispose();
    _calfUsageController.dispose();
    _homeUsageController.dispose();
    _notesController.dispose();
    _totalProductionController.dispose();
    super.dispose();
  }
}
