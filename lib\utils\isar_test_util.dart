import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../Dashboard/Farm Setup/models/farm_isar.dart';
import '../Dashboard/Farm Setup/services/farm_setup_handler.dart';
import '../services/database/isar_service.dart';

/// Utility class to test Isar database functionality
///
/// This class provides methods to:
/// - Create test data
/// - Run CRUD operations for testing
/// - Verify data integrity
class IsarTestUtil {
  static final Logger _logger = Logger('IsarTestUtil');
  
  /// Test Isar farm repository functionality
  static Future<void> testFarmRepository(BuildContext context) async {
    bool? mounted = true;
    String resultMessage = '';

    try {
      _logger.info('Starting Farm repository test...');
      
      final farmSetupHandler = GetIt.instance<FarmSetupHandler>();
      final isarService = GetIt.instance<IsarService>();
      
      // 1. Count farms
      final farms = await farmSetupHandler.getAllFarms();
      final initialCount = farms.length;
      _logger.info('Initial farm count: $initialCount');
      
      // 2. Create a test farm
      final testFarm = FarmIsar()
        ..name = 'Test Farm ${DateTime.now().millisecondsSinceEpoch}'
        ..ownerName = 'Test Owner'
        ..ownerContact = '123-456-7890'
        ..ownerEmail = '<EMAIL>'
        ..farmType = FarmType.mixed
        ..cattleCount = 10
        ..capacity = 50
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      
      // 3. Save the farm using direct Isar API
      await isarService.isar.writeTxn(() async {
        await isarService.farmIsars.put(testFarm);
      });
      _logger.info('Created test farm with ID: ${testFarm.id}');
      
      // 4. Count farms again
      final farmsAfterAdd = await farmSetupHandler.getAllFarms();
      final countAfterAdd = farmsAfterAdd.length;
      _logger.info('Farm count after add: $countAfterAdd');
      
      // 5. Get the farm by ID
      final retrievedFarm = await farmSetupHandler.getAllFarms().then(
        (farms) => farms.firstWhere((farm) => farm.id == testFarm.id, 
                                   orElse: () => FarmIsar())
      );
      
      if (retrievedFarm.id > 0) {
        _logger.info('Retrieved farm: ${retrievedFarm.name}');
        resultMessage = 'Farm repository test completed successfully';
      } else {
        resultMessage = 'Error: Could not retrieve test farm';
      }
    } catch (e) {
      _logger.severe('Error during farm repository test: $e');
      resultMessage = 'Error: $e';
    }

    // Show result if context is still mounted
    if (mounted && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(resultMessage),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }
} 