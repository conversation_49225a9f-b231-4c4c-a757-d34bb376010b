import 'dart:async';
import 'package:logging/logging.dart';
import '../../../services/database/database_helper.dart';
import '../../Notifications/models/notification_isar.dart';
import '../../Notifications/services/notifications_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';

import '../services/milk_service.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/milk_record_isar.dart';

/// Service for generating and managing milk production alerts
class MilkAlertService {
  static final Logger _logger = Logger('MilkAlertService');
  
  late final DatabaseHelper _dbHelper;
  late final NotificationsHandler _notificationsHandler;
  late final FarmSetupHandler _farmSetupHandler;
  late final MilkService _milkService;
  late final CattleHandler _cattleHandler;
  
  Timer? _alertTimer;
  bool _isInitialized = false;
  
  MilkAlertService() {
    _dbHelper = DatabaseHelper.instance;
    _notificationsHandler = _dbHelper.notificationsHandler;
    _farmSetupHandler = _dbHelper.farmSetupHandler;
    _milkService = MilkService();
    _cattleHandler = _dbHelper.cattleHandler;
  }
  
  /// Initialize the alert service and start periodic checks
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _logger.info('Initializing Milk Alert Service');
      
      // Run initial alert generation
      await generateAlerts();
      
      // Set up periodic alert generation (every 6 hours)
      _alertTimer = Timer.periodic(
        const Duration(hours: 6),
        (_) => generateAlerts(),
      );
      
      _isInitialized = true;
      _logger.info('Milk Alert Service initialized successfully');
    } catch (e) {
      _logger.severe('Failed to initialize Milk Alert Service: $e');
    }
  }
  
  /// Dispose of the service and clean up resources
  void dispose() {
    _alertTimer?.cancel();
    _alertTimer = null;
    _isInitialized = false;
    _logger.info('Milk Alert Service disposed');
  }
  
  /// Generate all types of milk production alerts
  Future<void> generateAlerts() async {
    try {
      _logger.info('Starting alert generation');
      
      // Check if alerts are enabled
      final alertSettings = await _farmSetupHandler.getAlertSettings();
      if (!alertSettings.milkProductionAlerts) {
        _logger.info('Milk production alerts are disabled');
        return;
      }
      
      // Generate different types of alerts
      await Future.wait([
        _generateLowProductionAlerts(),
        _generateMissedMilkingAlerts(),
        _generateProductionTrendAlerts(),
        _generateZeroProductionAlerts(),
      ]);
      
      _logger.info('Alert generation completed');
    } catch (e) {
      _logger.severe('Error generating alerts: $e');
    }
  }
  
  /// Generate alerts for cattle with significantly low production
  Future<void> _generateLowProductionAlerts() async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 7));
      
      final records = await _milkService.getMilkRecordsByDateRange(startDate, endDate);
      final cattle = await _cattleHandler.getAllCattle();
      
      // Group records by cattle
      final Map<String, List<MilkRecordIsar>> cattleRecords = {};
      for (var record in records) {
        if (record.cattleBusinessId != null) {
          cattleRecords.putIfAbsent(record.cattleBusinessId!, () => []).add(record);
        }
      }
      
      for (var entry in cattleRecords.entries) {
        final cattleId = entry.key;
        final cattleRecordsList = entry.value;
        
        if (cattleRecordsList.length >= 3) {
          final cattleInfo = cattle.firstWhere(
            (c) => c.businessId == cattleId,
            orElse: () => CattleIsar(),
          );
          
          await _checkLowProduction(cattleInfo, cattleRecordsList);
        }
      }
    } catch (e) {
      _logger.warning('Error generating low production alerts: $e');
    }
  }
  
  /// Check if a cattle has low production and create alert if needed
  Future<void> _checkLowProduction(CattleIsar cattle, List<MilkRecordIsar> records) async {
    try {
      // Calculate average production
      final totalProduction = records.fold<double>(
        0.0,
        (sum, record) => sum + (record.morningAmount ?? 0) + (record.eveningAmount ?? 0),
      );
      final avgDaily = totalProduction / records.length;
      
      // Get latest production
      records.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      final latestRecord = records.first;
      final latestProduction = (latestRecord.morningAmount ?? 0) + (latestRecord.eveningAmount ?? 0);
      
      // Check if production is significantly below average (30% drop)
      if (latestProduction < avgDaily * 0.7 && avgDaily > 0) {
        final dropPercentage = ((avgDaily - latestProduction) / avgDaily * 100);
        
        await _createAlert(
          type: 'production',
          title: 'Low Milk Production Alert',
          message: '${cattle.name ?? 'Unknown'} (${cattle.tagId ?? 'Unknown'}) '
                  'produced ${latestProduction.toStringAsFixed(1)}L today, '
                  '${dropPercentage.toStringAsFixed(1)}% below average (${avgDaily.toStringAsFixed(1)}L)',
          priority: dropPercentage > 50 ? 'high' : 'medium',
          cattleId: cattle.businessId,
          recordId: latestRecord.businessId,
        );
      }
    } catch (e) {
      _logger.warning('Error checking low production for cattle ${cattle.businessId}: $e');
    }
  }
  
  /// Generate alerts for missed milking sessions
  Future<void> _generateMissedMilkingAlerts() async {
    try {
      final today = DateTime.now();
      final yesterday = today.subtract(const Duration(days: 1));
      
      // Get all producing cattle
      final cattle = await _cattleHandler.getAllCattle();
      final producingCattle = cattle.where((c) => 
        c.status?.toLowerCase() == 'producing' || 
        c.status?.toLowerCase() == 'lactating'
      ).toList();
      
      for (var cattleItem in producingCattle) {
        final records = await _milkService.getMilkRecordsByDateRange(
          yesterday,
          today,
          cattleTagId: cattleItem.tagId,
        );
        
        // Check if there are any records for today
        final todayRecords = records.where((r) => 
          r.date != null && 
          r.date!.day == today.day &&
          r.date!.month == today.month &&
          r.date!.year == today.year
        ).toList();
        
        if (todayRecords.isEmpty && today.hour > 10) { // After 10 AM
          await _createAlert(
            type: 'production',
            title: 'Missed Milking Alert',
            message: 'No milk records found for ${cattleItem.name ?? 'Unknown'} '
                    '(${cattleItem.tagId ?? 'Unknown'}) today. Please check if milking was missed.',
            priority: 'medium',
            cattleId: cattleItem.businessId,
          );
        }
      }
    } catch (e) {
      _logger.warning('Error generating missed milking alerts: $e');
    }
  }
  
  /// Generate alerts for production trends
  Future<void> _generateProductionTrendAlerts() async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 14));
      
      final records = await _milkService.getMilkRecordsByDateRange(startDate, endDate);
      final cattle = await _cattleHandler.getAllCattle();
      
      // Group records by cattle
      final Map<String, List<MilkRecordIsar>> cattleRecords = {};
      for (var record in records) {
        if (record.cattleBusinessId != null) {
          cattleRecords.putIfAbsent(record.cattleBusinessId!, () => []).add(record);
        }
      }
      
      for (var entry in cattleRecords.entries) {
        final cattleId = entry.key;
        final cattleRecordsList = entry.value;
        
        if (cattleRecordsList.length >= 7) { // Need at least a week of data
          final cattleInfo = cattle.firstWhere(
            (c) => c.businessId == cattleId,
            orElse: () => CattleIsar(),
          );
          
          await _checkProductionTrend(cattleInfo, cattleRecordsList);
        }
      }
    } catch (e) {
      _logger.warning('Error generating production trend alerts: $e');
    }
  }
  
  /// Check production trends and create alerts for declining production
  Future<void> _checkProductionTrend(CattleIsar cattle, List<MilkRecordIsar> records) async {
    try {
      // Sort records by date
      records.sort((a, b) => (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now()));
      
      // Split into first and second week
      final midPoint = records.length ~/ 2;
      final firstWeek = records.sublist(0, midPoint);
      final secondWeek = records.sublist(midPoint);
      
      // Calculate averages
      final firstWeekAvg = firstWeek.fold<double>(0.0, (sum, r) => 
        sum + (r.morningAmount ?? 0) + (r.eveningAmount ?? 0)) / firstWeek.length;
      final secondWeekAvg = secondWeek.fold<double>(0.0, (sum, r) => 
        sum + (r.morningAmount ?? 0) + (r.eveningAmount ?? 0)) / secondWeek.length;
      
      // Check for significant decline (>20% drop)
      if (firstWeekAvg > 0 && secondWeekAvg < firstWeekAvg * 0.8) {
        final declinePercentage = ((firstWeekAvg - secondWeekAvg) / firstWeekAvg * 100);
        
        await _createAlert(
          type: 'production',
          title: 'Production Decline Alert',
          message: '${cattle.name ?? 'Unknown'} (${cattle.tagId ?? 'Unknown'}) '
                  'shows declining production trend: ${declinePercentage.toStringAsFixed(1)}% '
                  'decrease over the past week (${firstWeekAvg.toStringAsFixed(1)}L → ${secondWeekAvg.toStringAsFixed(1)}L)',
          priority: declinePercentage > 30 ? 'high' : 'medium',
          cattleId: cattle.businessId,
        );
      }
    } catch (e) {
      _logger.warning('Error checking production trend for cattle ${cattle.businessId}: $e');
    }
  }
  
  /// Generate alerts for zero production
  Future<void> _generateZeroProductionAlerts() async {
    try {
      final today = DateTime.now();
      final records = await _milkService.getMilkRecordsForDate(today);
      
      // Find records with zero production
      final zeroProductionRecords = records.where((r) => 
        (r.morningAmount ?? 0) == 0 && (r.eveningAmount ?? 0) == 0
      ).toList();
      
      final cattle = await _cattleHandler.getAllCattle();
      
      for (var record in zeroProductionRecords) {
        final cattleInfo = cattle.firstWhere(
          (c) => c.businessId == record.cattleBusinessId,
          orElse: () => CattleIsar(),
        );
        
        await _createAlert(
          type: 'production',
          title: 'Zero Production Alert',
          message: '${cattleInfo.name ?? 'Unknown'} (${cattleInfo.tagId ?? 'Unknown'}) '
                  'recorded zero milk production today. Please verify if this is correct.',
          priority: 'low',
          cattleId: record.cattleBusinessId,
          recordId: record.businessId,
        );
      }
    } catch (e) {
      _logger.warning('Error generating zero production alerts: $e');
    }
  }
  
  /// Create a new alert notification
  Future<void> _createAlert({
    required String type,
    required String title,
    required String message,
    required String priority,
    String? cattleId,
    String? recordId,
  }) async {
    try {
      // Check if similar alert already exists (avoid duplicates)
      final existingAlerts = await _notificationsHandler.getAllNotifications();
      final duplicateAlert = existingAlerts.where((n) => 
        n.cattleId == cattleId && 
        n.type == type && 
        n.title == title &&
        n.createdAt != null &&
        DateTime.now().difference(n.createdAt!).inHours < 24
      ).isNotEmpty;
      
      if (duplicateAlert) {
        _logger.fine('Skipping duplicate alert for cattle $cattleId');
        return;
      }
      
      final notification = NotificationIsar(
        businessId: 'alert_${DateTime.now().millisecondsSinceEpoch}_${cattleId ?? 'system'}',
        type: type,
        title: title,
        message: message,
        priority: priority,
        cattleId: cattleId,
        recordId: recordId,
        createdAt: DateTime.now(),
        isRead: false,
      );
      
      await _notificationsHandler.addNotification(notification);
      _logger.info('Created $priority priority $type alert for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error creating alert: $e');
    }
  }
}
