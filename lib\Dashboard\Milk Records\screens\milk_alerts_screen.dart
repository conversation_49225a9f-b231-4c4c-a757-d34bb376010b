import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../services/database/database_helper.dart';
import '../../Notifications/models/notification_isar.dart';
import '../../Notifications/services/notifications_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../Farm Setup/models/alert_settings_isar.dart';
import '../services/milk_service.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../../widgets/empty_state.dart';
import '../../../utils/message_utils.dart';

class MilkAlertsScreen extends StatefulWidget {
  const MilkAlertsScreen({super.key});

  @override
  State<MilkAlertsScreen> createState() => _MilkAlertsScreenState();
}

class _MilkAlertsScreenState extends State<MilkAlertsScreen> with TickerProviderStateMixin {
  // Color scheme matching milk module
  static const _primaryColor = Color(0xFF2E7D32); // Dark Green
  static const _accentColor = Color(0xFF4CAF50); // Light Green
  static const _redColor = Color(0xFFD32F2F); // Red for alerts
  static const _warningColor = Color(0xFF1976D2); // Blue for warnings

  late final DatabaseHelper _dbHelper;
  late final NotificationsHandler _notificationsHandler;
  late final FarmSetupHandler _farmSetupHandler;
  late final MilkService _milkService;
  late final CattleHandler _cattleHandler;

  List<NotificationIsar> _notifications = [];
  AlertSettingsIsar? _alertSettings;
  bool _isLoading = true;
  String _selectedFilter = 'All';
  late TabController _tabController;

  final List<String> _filterOptions = ['All', 'Unread', 'Production'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _dbHelper = DatabaseHelper.instance;
    _notificationsHandler = _dbHelper.notificationsHandler;
    _farmSetupHandler = _dbHelper.farmSetupHandler;
    _milkService = MilkService();
    _cattleHandler = _dbHelper.cattleHandler;
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // Load notifications and alert settings in parallel
      await Future.wait([
        _loadNotifications(),
        _loadAlertSettings(),
        _generateMilkProductionAlerts(),
      ]);

      if (mounted) {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      debugPrint('Error loading alert data: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadNotifications() async {
    try {
      final notifications = await _notificationsHandler.getAllNotifications();
      if (mounted) {
        setState(() {
          _notifications = notifications;
        });
      }
    } catch (e) {
      debugPrint('Error loading notifications: $e');
    }
  }

  Future<void> _loadAlertSettings() async {
    try {
      final settings = await _farmSetupHandler.getAlertSettings();
      if (mounted) {
        setState(() {
          _alertSettings = settings;
        });
      }
    } catch (e) {
      debugPrint('Error loading alert settings: $e');
    }
  }

  Future<void> _generateMilkProductionAlerts() async {
    try {
      if (_alertSettings?.milkProductionAlerts != true) return;

      // Get recent milk records (last 7 days)
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 7));

      final records = await _milkService.getMilkRecordsByDateRange(startDate, endDate);
      final cattle = await _cattleHandler.getAllCattle();

      // Group records by cattle
      final Map<String, List<dynamic>> cattleRecords = {};
      for (var record in records) {
        if (record.cattleBusinessId != null) {
          cattleRecords.putIfAbsent(record.cattleBusinessId!, () => []).add(record);
        }
      }

      // Generate alerts for low production
      for (var entry in cattleRecords.entries) {
        final cattleId = entry.key;
        final cattleRecordsList = entry.value;

        if (cattleRecordsList.length >= 3) { // Need at least 3 records for analysis
          final totalProduction = cattleRecordsList.fold<double>(
            0.0,
            (sum, record) => sum + (record.morningAmount ?? 0) + (record.eveningAmount ?? 0)
          );
          final avgDaily = totalProduction / cattleRecordsList.length;

          // Check if latest production is significantly below average
          final latestRecord = cattleRecordsList.last;
          final latestProduction = (latestRecord.morningAmount ?? 0) + (latestRecord.eveningAmount ?? 0);

          if (latestProduction < avgDaily * 0.7) { // 30% below average
            final cattleInfo = cattle.firstWhere(
              (c) => c.businessId == cattleId,
              orElse: () => cattle.first,
            );

            await _createProductionAlert(
              cattleInfo.name ?? 'Unknown',
              cattleInfo.tagId ?? 'Unknown',
              latestProduction,
              avgDaily,
              cattleId,
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error generating milk production alerts: $e');
    }
  }

  Future<void> _createProductionAlert(
    String cattleName,
    String tagId,
    double currentProduction,
    double avgProduction,
    String cattleId,
  ) async {
    try {
      final dropPercentage = ((avgProduction - currentProduction) / avgProduction * 100);

      final notification = NotificationIsar(
        businessId: 'alert_${DateTime.now().millisecondsSinceEpoch}_$cattleId',
        type: 'production',
        title: 'Low Milk Production Alert',
        message: '$cattleName ($tagId) produced ${currentProduction.toStringAsFixed(1)}L today, '
                '${dropPercentage.toStringAsFixed(1)}% below average (${avgProduction.toStringAsFixed(1)}L)',
        priority: dropPercentage > 40 ? 'high' : 'medium',
        cattleId: cattleId,
        createdAt: DateTime.now(),
        isRead: false,
      );

      // Check if similar alert already exists (avoid duplicates)
      final existingAlerts = _notifications.where((n) =>
        n.cattleId == cattleId &&
        n.type == 'production' &&
        n.createdAt != null &&
        DateTime.now().difference(n.createdAt!).inHours < 24
      ).toList();

      if (existingAlerts.isEmpty) {
        await _notificationsHandler.addNotification(notification);
        await _loadNotifications(); // Refresh the list
      }
    } catch (e) {
      debugPrint('Error creating production alert: $e');
    }
  }

  Future<void> _markAsRead(NotificationIsar notification) async {
    try {
      await _notificationsHandler.markNotificationAsRead(notification.businessId!);
      await _loadNotifications();
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  Future<void> _deleteNotification(NotificationIsar notification) async {
    try {
      await _notificationsHandler.deleteNotification(notification.businessId!);
      await _loadNotifications();

      if (mounted) {
        MilkMessageUtils.showSuccess(context, 'Alert deleted successfully');
      }
    } catch (e) {
      debugPrint('Error deleting notification: $e');
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      await _notificationsHandler.markAllNotificationsAsRead();
      await _loadNotifications();

      if (mounted) {
        MilkMessageUtils.showSuccess(context, 'All alerts marked as read');
      }
    } catch (e) {
      debugPrint('Error marking all as read: $e');
    }
  }

  List<NotificationIsar> get _filteredNotifications {
    switch (_selectedFilter) {
      case 'Unread':
        return _notifications.where((n) => !n.isRead).toList();
      case 'Production':
        return _notifications.where((n) => n.type == 'production').toList();
      default:
        return _notifications;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Milk Alerts',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: _primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 4,
        actions: [
          if (_notifications.any((n) => !n.isRead))
            IconButton(
              icon: const Icon(Icons.done_all),
              tooltip: 'Mark all as read',
              onPressed: _markAllAsRead,
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh alerts',
            onPressed: _loadData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Active', icon: Icon(Icons.notifications_active)),
            Tab(text: 'History', icon: Icon(Icons.history)),
            Tab(text: 'Settings', icon: Icon(Icons.settings)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: _primaryColor),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildActiveAlertsTab(),
                _buildHistoryTab(),
                _buildSettingsTab(),
              ],
            ),
    );
  }

  Widget _buildActiveAlertsTab() {
    final activeAlerts = _notifications.where((n) => !n.isRead).toList();

    if (activeAlerts.isEmpty) {
      return const MilkEmptyState(
        icon: Icons.notifications_off_outlined,
        message: 'No Active Alerts',
        subtitle: 'All alerts have been addressed or no issues detected',
        color: _primaryColor,
      );
    }

    return Column(
      children: [
        // Filter bar
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Filter:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _filterOptions.map((filter) {
                      final isSelected = _selectedFilter == filter;
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(filter),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedFilter = filter;
                            });
                          },
                          selectedColor: _primaryColor.withValues(alpha: 0.2),
                          checkmarkColor: _primaryColor,
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Alerts list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _filteredNotifications.length,
            itemBuilder: (context, index) {
              final notification = _filteredNotifications[index];
              return _buildAlertCard(notification);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryTab() {
    final readAlerts = _notifications.where((n) => n.isRead).toList();

    if (readAlerts.isEmpty) {
      return const MilkEmptyState(
        icon: Icons.history_outlined,
        message: 'No Alert History',
        subtitle: 'Resolved alerts will appear here',
        color: _primaryColor,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: readAlerts.length,
      itemBuilder: (context, index) {
        final notification = readAlerts[index];
        return _buildAlertCard(notification, isHistory: true);
      },
    );
  }

  Widget _buildSettingsTab() {
    if (_alertSettings == null) {
      return const Center(
        child: CircularProgressIndicator(color: _primaryColor),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.settings, color: _primaryColor),
                      SizedBox(width: 8),
                      Text(
                        'Alert Preferences',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Milk Production Alerts'),
                    subtitle: const Text('Get notified when production drops significantly'),
                    value: _alertSettings!.milkProductionAlerts,
                    activeColor: _primaryColor,
                    onChanged: (value) async {
                      setState(() {
                        _alertSettings!.milkProductionAlerts = value;
                      });
                      await _farmSetupHandler.saveAlertSettings(_alertSettings!);
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Push Notifications'),
                    subtitle: const Text('Receive notifications on your device'),
                    value: _alertSettings!.pushNotifications,
                    activeColor: _primaryColor,
                    onChanged: (value) async {
                      setState(() {
                        _alertSettings!.pushNotifications = value;
                      });
                      await _farmSetupHandler.saveAlertSettings(_alertSettings!);
                    },
                  ),
                  SwitchListTile(
                    title: const Text('In-App Notifications'),
                    subtitle: const Text('Show notifications within the app'),
                    value: _alertSettings!.inAppNotifications,
                    activeColor: _primaryColor,
                    onChanged: (value) async {
                      setState(() {
                        _alertSettings!.inAppNotifications = value;
                      });
                      await _farmSetupHandler.saveAlertSettings(_alertSettings!);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  Widget _buildAlertCard(NotificationIsar notification, {bool isHistory = false}) {
    final isUnread = !notification.isRead;
    final priorityColor = _getPriorityColor(notification.priority);
    final typeIcon = _getTypeIcon(notification.type);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isUnread ? 4 : 2,
      child: InkWell(
        onTap: isUnread ? () => _markAsRead(notification) : null,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: priorityColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      typeIcon,
                      color: priorityColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                notification.title ?? 'Alert',
                                style: TextStyle(
                                  fontWeight: isUnread ? FontWeight.bold : FontWeight.w500,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            if (isUnread)
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: _primaryColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        if (notification.priority != null)
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: priorityColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: priorityColor.withValues(alpha: 0.3)),
                            ),
                            child: Text(
                              notification.priority!.toUpperCase(),
                              style: TextStyle(
                                color: priorityColor,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (!isHistory)
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'mark_read':
                            _markAsRead(notification);
                            break;
                          case 'delete':
                            _deleteNotification(notification);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        if (isUnread)
                          const PopupMenuItem(
                            value: 'mark_read',
                            child: Row(
                              children: [
                                Icon(Icons.mark_email_read),
                                SizedBox(width: 8),
                                Text('Mark as Read'),
                              ],
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                notification.message ?? 'No details available',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (notification.createdAt != null)
                    Text(
                      DateFormat('MMM dd, yyyy HH:mm').format(notification.createdAt!),
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                  if (isHistory && notification.readAt != null)
                    Text(
                      'Read: ${DateFormat('MMM dd, HH:mm').format(notification.readAt!)}',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(String? priority) {
    switch (priority?.toLowerCase()) {
      case 'high':
        return _redColor;
      case 'medium':
        return _warningColor;
      case 'low':
        return _accentColor;
      default:
        return _primaryColor;
    }
  }

  IconData _getTypeIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'production':
        return Icons.opacity;
      case 'feed':
        return Icons.grass;
      default:
        return Icons.notifications;
    }
  }


}