import 'package:flutter/material.dart';
import 'filter_models.dart';
import 'filter_utils.dart';

/// Reusable filter and search component for cattle management screens
class ReusableFilterSearch extends StatelessWidget {
  /// Configuration for the filter component
  final FilterConfig config;
  
  /// Current filter state
  final FilterState filterState;
  
  /// Data needed for filtering
  final FilterData filterData;
  
  /// Search controller
  final TextEditingController searchController;
  
  /// Callback when any filter changes
  final FilterStateCallback onFilterChanged;

  /// Callback when search query changes
  final SearchQueryCallback onSearchChanged;
  
  /// Callback when clear all filters is pressed
  final ClearFiltersCallback onClearFilters;

  /// Total number of records (for record count display)
  final int? totalRecords;

  /// Number of filtered records (for record count display)
  final int? filteredRecords;

  const ReusableFilterSearch({
    Key? key,
    required this.config,
    required this.filterState,
    required this.filterData,
    required this.searchController,
    required this.onFilterChanged,
    required this.onSearchChanged,
    required this.onClearFilters,
    this.totalRecords,
    this.filteredRecords,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          _buildSearchRow(),
          
          // Filter Row
          _buildFilterRow(),
          
          // Clear Filters Button (only show when filters are applied)
          if (config.showClearAllButton && filterState.hasActiveFilters)
            _buildClearFiltersSection(),

          const Divider(height: 1),
        ],
      ),
    );
  }

  Widget _buildSearchRow() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: searchController,
        decoration: InputDecoration(
          hintText: config.searchHintText,
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          final newState = filterState.copyWith(searchQuery: value);
          onSearchChanged(value);
          onFilterChanged(newState);
        },
      ),
    );
  }

  Widget _buildFilterRow() {
    final filters = <Widget>[];

    // Animal Type Filter
    if (config.showAnimalTypeFilter) {
      filters.add(_buildAnimalTypeFilter());
    }

    // Cattle Filter
    if (config.showCattleFilter) {
      filters.add(_buildCattleFilter());
    }

    // Date Range Filter
    if (config.showDateRangeFilter) {
      filters.add(_buildDateRangeFilter());
    }

    if (filters.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      child: Row(
        children: filters
            .expand((filter) => [filter, const SizedBox(width: 8)])
            .take(filters.length * 2 - 1)
            .toList(),
      ),
    );
  }

  Widget _buildAnimalTypeFilter() {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: PopupMenuButton<String>(
          position: PopupMenuPosition.under,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'All',
              child: Text('All Types'),
            ),
            ...filterData.animalTypes.map((type) => PopupMenuItem(
                  value: type.name ?? 'Unknown',
                  child: Text(type.name ?? 'Unknown'),
                )),
          ],
          onSelected: (value) {
            final newState = filterState.copyWith(selectedAnimalType: value);
            onFilterChanged(newState);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    filterState.selectedAnimalType,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCattleFilter() {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: PopupMenuButton<String>(
          position: PopupMenuPosition.under,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'All',
              child: Text('All Cattle'),
            ),
            ...filterData.filteredCattle.map((cattle) {
              final displayName = FilterUtils.getCattleDisplayName(cattle);
              return PopupMenuItem(
                value: cattle.tagId ?? '',
                child: Text(displayName),
              );
            }),
          ],
          onSelected: (value) {
            final newState = filterState.copyWith(selectedCattleId: value);
            onFilterChanged(newState);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    filterState.selectedCattleId == 'All'
                        ? 'All Cattle'
                        : FilterUtils.getCattleDisplayName(filterData.cattleMap[filterState.selectedCattleId]),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeFilter() {
    final dateOptions = config.customDateRangeOptions ?? DateRangeOptions.defaultOptions;
    
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: PopupMenuButton<String>(
          position: PopupMenuPosition.under,
          itemBuilder: (context) => dateOptions
              .map((range) => PopupMenuItem(
                    value: range,
                    child: Text(range),
                  ))
              .toList(),
          onSelected: (value) {
            final newState = filterState.copyWith(selectedDateRange: value);
            onFilterChanged(newState);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    filterState.selectedDateRange,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildClearFiltersSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                searchController.clear();
                onClearFilters();
              },
              icon: const Icon(Icons.clear),
              label: const Text('Clear Filters'),
            ),
          ),
          if (config.showRecordCount &&
              filteredRecords != null &&
              totalRecords != null) ...[
            const SizedBox(width: 16),
            Text(
              '$filteredRecords of $totalRecords records',
              style: const TextStyle(
                color: Color(0xFF2E7D32), // Using green color to match health theme
              ),
            ),
          ],
        ],
      ),
    );
  }
}
