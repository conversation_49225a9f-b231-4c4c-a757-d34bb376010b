import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import '../dialogs/vaccination_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Cattle/widgets/vaccination_history_card.dart';
import '../../../services/database/database_helper.dart';
import '../models/vaccination_record_isar.dart';
import 'dart:async'; // Add this for StreamSubscription
import 'package:intl/intl.dart';
import '../../../utils/message_utils.dart';
import '../../Cattle/screens/cattle_detail_screen.dart';
import '../../widgets/reusable_filter_search.dart';
import '../../widgets/filter_models.dart';
import '../../widgets/filter_utils.dart';

class VaccinationsScreen extends StatefulWidget {
  const VaccinationsScreen({Key? key}) : super(key: key);

  @override
  State<VaccinationsScreen> createState() => _VaccinationsScreenState();
}

class _VaccinationsScreenState extends State<VaccinationsScreen> {
  final TextEditingController _searchController = TextEditingController();
  late final DatabaseHelper _dbHelper;

  List<VaccinationIsar> _vaccinations = [];
  List<VaccinationIsar> _filteredVaccinations = [];
  Map<String, CattleIsar> _cattleMap = {};
  List<dynamic> _animalTypes = [];
  Map<String, String> _animalTypeIdToName = {};
  List<CattleIsar> _filteredCattle = [];
  bool _isLoading = true;

  // Filter state using reusable component
  late FilterState _filterState;

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _vaccinationSubscription;

  @override
  void initState() {
    super.initState();
    _dbHelper = DatabaseHelper.instance;
    _filterState = FilterState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _vaccinationSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load animal types using DatabaseHelper
      final animalTypes = await _dbHelper.farmSetupHandler.getAllAnimalTypes();

      // Create maps for efficient lookups
      _animalTypeIdToName = {
        for (var type in animalTypes) type.businessId ?? '': type.name ?? ''
      };

      // Load all cattle using DatabaseHelper
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      // Create cattle map with both tagId and businessId as keys for robust lookup
      final cattleMap = <String, CattleIsar>{};
      for (var cattle in allCattle) {
        if (cattle.tagId?.isNotEmpty == true) {
          cattleMap[cattle.tagId!] = cattle;
        }
        if (cattle.businessId?.isNotEmpty == true) {
          cattleMap[cattle.businessId!] = cattle;
        }
      }

      // Migrate existing vaccinations to have recordId (one-time operation)
      print('🔵 Running vaccination recordId migration...');
      await _dbHelper.healthHandler.migrateVaccinationRecordIds();

      // Get all vaccinations
      final allVaccinations = await _dbHelper.healthHandler.getAllVaccinations();
      print('🔵 Loaded ${allVaccinations.length} vaccinations');

      // Debug: Check vaccination data quality
      int validVaccinations = 0;
      int invalidVaccinations = 0;

      for (var vaccination in allVaccinations) {
        if (vaccination.id == Isar.autoIncrement || vaccination.id < 0) {
          invalidVaccinations++;
          print('⚠️ Invalid vaccination found: ID=${vaccination.id}, recordId=${vaccination.recordId}, vaccine=${vaccination.vaccineName}');
        } else {
          validVaccinations++;
        }
      }

      print('🔵 Valid vaccinations: $validVaccinations');
      print('🔵 Invalid vaccinations: $invalidVaccinations');

      // Update filtered cattle list
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: allCattle,
        selectedAnimalType: _filterState.selectedAnimalType,
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: null, // Health module shows all cattle
      );

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _vaccinations = allVaccinations;
          _animalTypes = animalTypes;
          _isLoading = false;
        });

        // Apply initial filtering
        _filterRecords();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (context.mounted) {
          MessageUtils.showError(context, 'Error loading vaccinations: $e');
        }
      }
    }
  }

  void _onFilterChanged(FilterState newFilterState) {
    setState(() {
      _filterState = newFilterState;

      // Update filtered cattle when animal type changes
      if (_filterState.selectedAnimalType != newFilterState.selectedAnimalType) {
        _filteredCattle = FilterUtils.filterCattle(
          allCattle: _cattleMap.values.toList(),
          selectedAnimalType: newFilterState.selectedAnimalType,
          animalTypeIdToName: _animalTypeIdToName,
          genderFilter: null, // Health module shows all cattle
        );

        // Clear cattle selection if it's no longer valid
        if (!FilterUtils.isCattleIdValid(newFilterState.selectedCattleId, _filteredCattle)) {
          _filterState = _filterState.copyWith(selectedCattleId: 'All');
        }
      }
    });

    _filterRecords();
  }

  void _onSearchChanged(String searchQuery) {
    // This is called immediately when search text changes
    // The actual filtering happens in _onFilterChanged
  }

  void _onClearFilters() {
    setState(() {
      _filterState.clearAll();
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: _cattleMap.values.toList(),
        selectedAnimalType: 'All',
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: null,
      );
    });
    _filterRecords();
  }

  void _filterRecords() {
    _filteredVaccinations = FilterUtils.filterRecords<VaccinationIsar>(
      records: _vaccinations,
      filterState: _filterState,
      cattleMap: _cattleMap,
      animalTypeIdToName: _animalTypeIdToName,
      getCattleId: (record) => record.cattleId ?? '',
      getRecordDate: (record) => record.date,
      getSearchableFields: (record) => [
        record.vaccineName ?? '',
        record.manufacturer ?? '',
        record.batchNumber ?? '',
        record.notes ?? '',
      ],
    );

    // Sort records by date (newest first)
    _filteredVaccinations.sort((a, b) {
      if (a.date == null && b.date == null) return 0;
      if (a.date == null) return 1;
      if (b.date == null) return -1;
      return b.date!.compareTo(a.date!);
    });

    setState(() {});
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Vaccinations',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Get unique cattle list to avoid duplicates from the map
          final uniqueCattle = <String, CattleIsar>{};
          for (var cattle in _cattleMap.values) {
            if (cattle.businessId?.isNotEmpty == true) {
              uniqueCattle[cattle.businessId!] = cattle;
            }
          }

          showDialog<void>(
            context: context,
            builder: (context) {
              return VaccinationFormDialog(
                cattleId: '',
                cattle: uniqueCattle.values.toList(),
                onSave: (vaccination) async {
                  await _dbHelper.healthHandler.addOrUpdateVaccination(
                    vaccination.cattleId ?? '',
                    vaccination
                  );
                  _loadData();
                  return true;
                },
              );
            },
          );
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Reusable Filter and Search Component
          ReusableFilterSearch(
            config: FilterConfig.health,
            filterState: _filterState,
            filterData: FilterData(
              cattleMap: _cattleMap,
              animalTypes: _animalTypes,
              animalTypeIdToName: _animalTypeIdToName,
              filteredCattle: _filteredCattle,
            ),
            searchController: _searchController,
            onFilterChanged: _onFilterChanged,
            onSearchChanged: _onSearchChanged,
            onClearFilters: _onClearFilters,
            totalRecords: _vaccinations.length,
            filteredRecords: _filteredVaccinations.length,
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredVaccinations.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.vaccines,
                                size: 64,
                              color: Colors.grey[500],
                              ),
                              const SizedBox(height: 16),
                              Text(
                              'No Vaccination Records Found',
                                style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                              'Add a new vaccination record',
                                style: TextStyle(
                                fontSize: 16,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(height: 24),
                              ElevatedButton.icon(
                                onPressed: () {
                                // Get unique cattle list to avoid duplicates from the map
                                final uniqueCattle = <String, CattleIsar>{};
                                for (var cattle in _cattleMap.values) {
                                  if (cattle.businessId?.isNotEmpty == true) {
                                    uniqueCattle[cattle.businessId!] = cattle;
                                  }
                                }

                                showDialog<void>(
                                    context: context,
                                    builder: (context) {
                                      return VaccinationFormDialog(
                                        cattleId: '',
                                        cattle: uniqueCattle.values.toList(),
                                        onSave: (vaccination) async {
                                          await _dbHelper.healthHandler.addOrUpdateVaccination(
                                            vaccination.cattleId ?? '',
                                            vaccination
                                          );
                                          _loadData();
                                          return true;
                                        },
                                      );
                                    },
                                );
                                },
                                icon: const Icon(Icons.add),
                                label: const Text('Add Vaccination'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF2E7D32),
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: _loadData,
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                _buildRecordsList(),
                                // Add padding at the bottom for the FAB
                                const SizedBox(height: 80),
                              ],
                            ),
                          ),
                        ),
                ),
              ],
            ),
    );
  }

  // Navigate to cattle detail screen with health tab (vaccinations sub-tab)
  void _navigateToCattleHealthTab(String cattleId) {
    final cattle = _cattleMap[cattleId];
    if (cattle != null) {
      // Determine correct health tab index based on cattle gender
      // Female cattle: Overview(0), Family Tree(1), Breeding(2), Health(3), Milk(4), Events(5)
      // Male cattle: Overview(0), Family Tree(1), Health(2), Events(3)
      final isFemale = (cattle.gender?.toLowerCase() ?? '') == 'female';
      final healthTabIndex = isFemale ? 3 : 2;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CattleDetailScreen(
            existingCattle: cattle,
            businessId: cattle.businessId ?? '',
            onCattleUpdated: (updatedCattle) {
              // Handle cattle update if needed
            },
            initialTabIndex: healthTabIndex, // Health tab
            initialHealthTabIndex: 2, // Vaccinations sub-tab
          ),
        ),
      );
    }
  }

  Widget _buildRecordsList() {
    // Prepare records with cattle name and ID info for display
    final recordsForDisplay = _filteredVaccinations.map((vaccination) {
      final cattle = _cattleMap[vaccination.cattleId ?? ''];
      final vaccinationMap = vaccination.toMap();
      vaccinationMap['cattleName'] = cattle?.name ?? 'Unknown Cattle';
      vaccinationMap['cattleId'] = cattle?.tagId ?? vaccination.cattleId ?? 'Unknown';  // Use cattle tag ID for display, fallback to stored cattleId
      return vaccinationMap;
    }).toList();

    return VaccinationHistoryCard(
      records: recordsForDisplay,
      title: 'Vaccination Records',
      emptyMessage: 'No vaccination records found',
      onEdit: (record) => _editVaccination(_getVaccinationFromMap(record)),
      onDelete: (record) {
        print('🔵 VaccinationHistoryCard onDelete called');
        print('🔵 Record keys: ${record.keys.toList()}');
        print('🔵 Record id: ${record['id']}');
        print('🔵 Full record: $record');

        final vaccination = _getVaccinationFromMap(record);
        print('🔵 Vaccination object created:');
        print('🔵   - Database ID: ${vaccination.id}');
        print('🔵   - Record ID: ${vaccination.recordId}');
        print('🔵   - Cattle ID: ${vaccination.cattleId}');
        print('🔵   - Vaccine Name: ${vaccination.vaccineName}');

        return _deleteVaccination(vaccination);
      },
      onCattleTap: (record) {
        // Navigate to cattle detail screen with health tab
        // Use the actual tagId from the cattle record for navigation
        final vaccination = _getVaccinationFromMap(record);
        final cattle = _cattleMap[vaccination.cattleId ?? ''];
        if (cattle?.tagId != null) {
          _navigateToCattleHealthTab(cattle!.tagId!);
        }
      },
    );
  }

  // Helper method to convert map back to VaccinationIsar
  VaccinationIsar _getVaccinationFromMap(Map<String, dynamic> vaccinationMap) {
    return VaccinationIsar.fromMap(vaccinationMap);
  }



  void _editVaccination(VaccinationIsar vaccination) {
    final cattle = _cattleMap[vaccination.cattleId ?? ''];
    if (cattle == null) return;

    // Get unique cattle list to avoid duplicates from the map
    final uniqueCattle = <String, CattleIsar>{};
    for (var cattle in _cattleMap.values) {
      if (cattle.businessId?.isNotEmpty == true) {
        uniqueCattle[cattle.businessId!] = cattle;
      }
    }

    showDialog(
      context: context,
      builder: (context) {
        return VaccinationFormDialog(
          cattleId: vaccination.cattleId ?? '',
          cattle: uniqueCattle.values.toList(),
          vaccination: vaccination,
          onSave: (updatedVaccination) async {
            await _dbHelper.healthHandler.addOrUpdateVaccination(
              updatedVaccination.cattleId ?? '',
              updatedVaccination
            );
            _loadData();
            return true;
          },
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
      children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteVaccination(VaccinationIsar vaccination) async {
    print('🔵 _deleteVaccination called');
    print('🔵 Vaccination recordId: ${vaccination.recordId}');
    print('🔵 Vaccination database ID: ${vaccination.id}');
    print('🔵 Vaccination cattleId: ${vaccination.cattleId}');

    // Check if this is a valid database record
    if (vaccination.id == Isar.autoIncrement || vaccination.id < 0) {
      print('🔴 Invalid vaccination record - not properly saved to database');
      print('🔴 Database ID: ${vaccination.id} (this indicates unsaved record)');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error: This vaccination record was not properly saved. Please refresh and try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Use recordId if available, otherwise use database ID as string
    final recordId = vaccination.recordId;
    final databaseId = vaccination.id.toString();
    final id = (recordId != null && recordId.isNotEmpty) ? recordId : databaseId;
    final cattleId = vaccination.cattleId;

    print('🔵 Using ID for deletion: "$id" (type: ${recordId != null && recordId.isNotEmpty ? 'recordId' : 'databaseId'})');

    if (cattleId == null) {
      print('🔴 Cattle ID is null - cannot proceed');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error: Cattle ID is missing'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: const Text(
            'Are you sure you want to delete this vaccination record? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                print('🔵 Calling deleteVaccination with ID: "$id"');
                await _dbHelper.healthHandler.deleteVaccination(id);
                print('🟢 Vaccination deletion completed successfully');
                await _loadData(); // Reload data after deletion
                if (context.mounted) {
                  MessageUtils.showSuccess(context, 'Vaccination record deleted successfully');
                }
              } catch (e) {
                print('🔴 Error deleting vaccination: $e');
                if (context.mounted) {
                  MessageUtils.showError(context, 'Error deleting vaccination record: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return DateFormat.yMMMd().format(date.toLocal());
  }


}
