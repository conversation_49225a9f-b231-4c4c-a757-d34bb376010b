import '../Cattle/models/cattle_isar.dart';
import 'filter_models.dart';

/// Utility class for common filter operations
class FilterUtils {
  /// Check if a date is within the specified date range
  static bool isWithinDateRange(DateTime? recordDate, String range) {
    if (recordDate == null) return false;

    final now = DateTime.now();
    switch (range) {
      case 'Today':
        final today = DateTime(now.year, now.month, now.day);
        return recordDate.isAfter(today.subtract(const Duration(days: 1)));
      case '7 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 7)));
      case '30 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 30)));
      case '90 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 90)));
      case '6 Months':
        return recordDate.isAfter(now.subtract(const Duration(days: 180)));
      case '1 Year':
        return recordDate.isAfter(now.subtract(const Duration(days: 365)));
      case 'All Time':
      default:
        return true;
    }
  }

  /// Check if a string date is within the specified date range
  static bool isStringDateWithinRange(String? dateStr, String range) {
    if (dateStr == null) return false;
    final recordDate = DateTime.tryParse(dateStr);
    return isWithinDateRange(recordDate, range);
  }

  /// Filter cattle based on animal type and gender
  static List<CattleIsar> filterCattle({
    required List<CattleIsar> allCattle,
    required String selectedAnimalType,
    required Map<String, String> animalTypeIdToName,
    String? genderFilter,
  }) {
    List<CattleIsar> filtered = List.from(allCattle);

    // Apply gender filter if specified
    if (genderFilter != null) {
      filtered = filtered
          .where((cattle) => cattle.gender?.toLowerCase() == genderFilter.toLowerCase())
          .toList();
    }

    // Apply animal type filter
    if (selectedAnimalType != 'All') {
      filtered = filtered
          .where((cattle) => animalTypeIdToName[cattle.animalTypeId] == selectedAnimalType)
          .toList();
    }

    // Sort by name
    filtered.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    return filtered;
  }

  /// Generic record filtering function
  static List<T> filterRecords<T>({
    required List<T> records,
    required FilterState filterState,
    required Map<String, CattleIsar> cattleMap,
    required Map<String, String> animalTypeIdToName,
    required String Function(T record) getCattleId,
    required DateTime? Function(T record) getRecordDate,
    required List<String> Function(T record) getSearchableFields,
  }) {
    List<T> filtered = List.from(records);

    // Apply animal type filter
    if (filterState.selectedAnimalType != 'All') {
      filtered = filtered.where((record) {
        final cattle = cattleMap[getCattleId(record)];
        return cattle != null &&
            animalTypeIdToName[cattle.animalTypeId] == filterState.selectedAnimalType;
      }).toList();
    }

    // Apply cattle filter
    if (filterState.selectedCattleId != 'All') {
      filtered = filtered
          .where((record) => getCattleId(record) == filterState.selectedCattleId)
          .toList();
    }

    // Apply date range filter
    if (filterState.selectedDateRange != 'All Time') {
      filtered = filtered.where((record) {
        return isWithinDateRange(getRecordDate(record), filterState.selectedDateRange);
      }).toList();
    }

    // Apply search filter
    if (filterState.searchQuery.isNotEmpty) {
      final query = filterState.searchQuery.toLowerCase();
      filtered = filtered.where((record) {
        final cattle = cattleMap[getCattleId(record)];
        if (cattle == null) return false;

        // Search in cattle name and tag ID
        final cattleName = cattle.name?.toLowerCase() ?? '';
        final cattleTagId = cattle.tagId?.toLowerCase() ?? '';
        
        if (cattleName.contains(query) || cattleTagId.contains(query)) {
          return true;
        }

        // Search in additional fields
        final searchableFields = getSearchableFields(record);
        return searchableFields.any((field) => 
            field.toLowerCase().contains(query));
      }).toList();
    }

    return filtered;
  }

  /// Update filtered cattle list when animal type changes
  static List<CattleIsar> updateFilteredCattle({
    required Map<String, CattleIsar> cattleMap,
    required String selectedAnimalType,
    required Map<String, String> animalTypeIdToName,
    String? genderFilter,
  }) {
    final allCattle = cattleMap.values.toList();
    return filterCattle(
      allCattle: allCattle,
      selectedAnimalType: selectedAnimalType,
      animalTypeIdToName: animalTypeIdToName,
      genderFilter: genderFilter,
    );
  }

  /// Check if a cattle ID is valid in the filtered cattle list
  static bool isCattleIdValid(String cattleId, List<CattleIsar> filteredCattle) {
    if (cattleId == 'All') return true;
    return filteredCattle.any((c) => c.tagId == cattleId);
  }

  /// Get display name for cattle (Name (TagID) format)
  static String getCattleDisplayName(CattleIsar? cattle) {
    if (cattle == null) return 'Unknown';
    if (cattle.name != null && cattle.tagId != null) {
      return '${cattle.name} (${cattle.tagId})';
    }
    return cattle.name ?? cattle.tagId ?? 'Unknown';
  }
}
