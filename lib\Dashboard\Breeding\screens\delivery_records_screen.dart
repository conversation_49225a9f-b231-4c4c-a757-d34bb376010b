import 'package:flutter/material.dart';
import 'dart:async';
import '../../../services/database/database_helper.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../dialogs/delivery_form_dialog.dart';
import '../../Cattle/widgets/delivery_history_card.dart';
import '../../Cattle/screens/cattle_detail_screen.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_colors.dart';
import '../../widgets/reusable_filter_search.dart';
import '../../widgets/filter_models.dart';
import '../../widgets/filter_utils.dart';

// Wrapper class to navigate directly to breeding tab with delivery sub-tab
class CattleDetailScreenWithDeliveryTab extends StatelessWidget {
  final CattleIsar cattle;
  final BreedCategoryIsar breed;
  final AnimalTypeIsar animalType;
  final Function(CattleIsar) onCattleUpdated;

  const CattleDetailScreenWithDeliveryTab({
    Key? key,
    required this.cattle,
    required this.breed,
    required this.animalType,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // For female cattle, the Breeding tab is at index 2 (Overview=0, Family Tree=1, Breeding=2, Health=3, Milk=4, Events=5)
    final isFemale = (cattle.gender?.toLowerCase() ?? '') == 'female';
    final breedingTabIndex = isFemale ? 2 : null; // Only set if female cattle

    return CattleDetailScreen(
      existingCattle: cattle,
      businessId: cattle.businessId ?? '',
      onCattleUpdated: onCattleUpdated,
      initialTabIndex: breedingTabIndex, // Navigate to Breeding tab
      initialBreedingTabIndex: 2, // Navigate to Delivery sub-tab (index 2)
    );
  }
}

class DeliveryRecordsScreen extends StatefulWidget {
  const DeliveryRecordsScreen({Key? key}) : super(key: key);

  @override
  State<DeliveryRecordsScreen> createState() => _DeliveryRecordsScreenState();
}

class _DeliveryRecordsScreenState extends State<DeliveryRecordsScreen> {
  final TextEditingController _searchController = TextEditingController();
  late final DatabaseHelper _databaseHelper;

  List<Map<String, dynamic>> _deliveryRecords = [];
  List<Map<String, dynamic>> _filteredRecords = [];
  Map<String, CattleIsar> _cattleMap = {};
  List<dynamic> _animalTypes = [];
  bool _isLoading = true;

  // Filter state using reusable component
  late FilterState _filterState;
  List<CattleIsar> _filteredCattle = [];
  Map<String, String> _animalTypeIdToName = {};

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _deliveryStreamSubscription;

  @override
  void initState() {
    super.initState();
    _databaseHelper = DatabaseHelper.instance;
    _filterState = FilterState();
    _loadData();
    _setupStreamListener();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _deliveryStreamSubscription?.cancel();
    super.dispose();
  }

  void _setupStreamListener() {
    _deliveryStreamSubscription =
        _databaseHelper.deliveryRecordStream.listen(_handleRecordUpdate);
  }

  // Handle updates from the stream
  void _handleRecordUpdate(Map<String, dynamic> event) {
    final action = event['action'] as String?;

    if (action == 'add' || action == 'update') {
      final recordMap = event['record'] as Map<String, dynamic>?;
      if (recordMap != null) {
        _updateLocalRecord(recordMap, action);
      }
    } else if (action == 'delete') {
      final recordId = event['recordId'] as String?;
      if (recordId != null) {
        _removeLocalRecord(recordId);
      }
    }
  }

  // Update a record in the local state
  void _updateLocalRecord(Map<String, dynamic> record, String? action) {
    _safeSetState(() {
      if (action == 'add') {
        // Add if not exists
        if (!_deliveryRecords.any((r) => r['id'] == record['id'])) {
          _deliveryRecords.add(record);
        }
      } else {
        // Update existing
        final index = _deliveryRecords.indexWhere((r) => r['id'] == record['id']);
        if (index >= 0) {
          _deliveryRecords[index] = record;
        } else {
          _deliveryRecords.add(record);
        }
      }

      // Sort by date (newest first)
      _deliveryRecords.sort((a, b) {
        final aDate = DateTime.tryParse(a['date'] ?? '') ?? DateTime.now();
        final bDate = DateTime.tryParse(b['date'] ?? '') ?? DateTime.now();
        return bDate.compareTo(aDate);
      });

    });

    // Update filtered records
    _filterRecords();
  }

  // Remove a record from the local state
  void _removeLocalRecord(String recordId) {
    _safeSetState(() {
      _deliveryRecords.removeWhere((r) => r['id'] == recordId);
    });
    _filterRecords();
  }

  // Safe setState that checks if mounted
  void _safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  Future<void> _loadData() async {
    _safeSetState(() => _isLoading = true);

    try {
      // Load all cattle for reference
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      _cattleMap = {for (var cattle in allCattle) cattle.tagId ?? '': cattle};

      // Load animal types for reference
      final animalTypesData =
          await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      _animalTypes = animalTypesData;
      _animalTypeIdToName = {
        for (var type in animalTypesData) type.businessId ?? '': type.name ?? ''
      };

      // Load delivery records from database
      final records = await _databaseHelper.breedingHandler.getAllDeliveryRecords();

      // Sort by date (newest first)
      records.sort((a, b) {
        final aDate = DateTime.tryParse(a['date'] ?? '') ?? DateTime.now();
        final bDate = DateTime.tryParse(b['date'] ?? '') ?? DateTime.now();
        return bDate.compareTo(aDate);
      });

      // Update filtered cattle list
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: allCattle,
        selectedAnimalType: _filterState.selectedAnimalType,
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: 'female', // Breeding module shows only female cattle
      );

      _safeSetState(() {
        _deliveryRecords = records;
        _isLoading = false;
      });

      // Apply initial filtering
      _filterRecords();
    } catch (e) {
      _safeSetState(() => _isLoading = false);
      if (mounted) {
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  // Get user-friendly error message
  String _getReadableErrorMessage(dynamic error) {
    if (error is Exception) {
      final message = error.toString().replaceAll('Exception: ', '');
      return message.isNotEmpty
          ? message
          : 'An error occurred while loading records';
    }
    return 'Error loading delivery records: $error';
  }

  void _onFilterChanged(FilterState newFilterState) {
    _safeSetState(() {
      _filterState = newFilterState;

      // Update filtered cattle when animal type changes
      if (_filterState.selectedAnimalType != newFilterState.selectedAnimalType) {
        _filteredCattle = FilterUtils.filterCattle(
          allCattle: _cattleMap.values.toList(),
          selectedAnimalType: newFilterState.selectedAnimalType,
          animalTypeIdToName: _animalTypeIdToName,
          genderFilter: 'female', // Breeding module shows only female cattle
        );

        // Clear cattle selection if it's no longer valid
        if (!FilterUtils.isCattleIdValid(newFilterState.selectedCattleId, _filteredCattle)) {
          _filterState = _filterState.copyWith(selectedCattleId: 'All');
        }
      }
    });

    _filterRecords();
  }

  void _onSearchChanged(String searchQuery) {
    // This is called immediately when search text changes
    // The actual filtering happens in _onFilterChanged
  }

  void _onClearFilters() {
    _safeSetState(() {
      _filterState.clearAll();
      _filteredCattle = FilterUtils.filterCattle(
        allCattle: _cattleMap.values.toList(),
        selectedAnimalType: 'All',
        animalTypeIdToName: _animalTypeIdToName,
        genderFilter: 'female',
      );
    });
    _filterRecords();
  }

  void _filterRecords() {
    // Custom filtering for delivery records (Map<String, dynamic>)
    List<Map<String, dynamic>> filtered = List.from(_deliveryRecords);

    // Apply search filter
    if (_filterState.searchQuery.isNotEmpty) {
      final query = _filterState.searchQuery.toLowerCase();
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record['cattleId']];
        if (cattle == null) return false;

        final cattleName = cattle.name?.toLowerCase() ?? '';
        final cattleId = record['cattleId']?.toLowerCase() ?? '';

        return cattleName.contains(query) ||
            cattleId.contains(query) ||
            (record['notes']?.toLowerCase() ?? '').contains(query);
      }).toList();
    }

    // Apply animal type filter
    if (_filterState.selectedAnimalType != 'All') {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record['cattleId']];
        return cattle != null &&
            _animalTypeIdToName[cattle.animalTypeId] == _filterState.selectedAnimalType;
      }).toList();
    }

    // Apply cattle filter
    if (_filterState.selectedCattleId != 'All') {
      filtered = filtered
          .where((record) => record['cattleId'] == _filterState.selectedCattleId)
          .toList();
    }

    // Apply date range filter
    if (_filterState.selectedDateRange != 'All Time') {
      filtered = filtered.where((record) {
        return _isWithinDateRange(record['date'], _filterState.selectedDateRange);
      }).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) {
      final aDate = DateTime.tryParse(a['date'] ?? '') ?? DateTime.now();
      final bDate = DateTime.tryParse(b['date'] ?? '') ?? DateTime.now();
      return bDate.compareTo(aDate);
    });

    _safeSetState(() {
      _filteredRecords = filtered;
    });
  }

  bool _isWithinDateRange(String? dateStr, String range) {
    if (dateStr == null) return false;
    
    final recordDate = DateTime.tryParse(dateStr);
    if (recordDate == null) return false;

    final now = DateTime.now();
    switch (range) {
      case 'Today':
        return recordDate.isAfter(DateTime(now.year, now.month, now.day));
      case '7 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 7)));
      case '30 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 30)));
      case '90 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 90)));
      default:
        return true;
    }
  }

  Future<void> _refreshRecords() async {
    _safeSetState(() => _isLoading = true);
    await _loadData();
  }

  // Handle edit record
  Future<void> _editDeliveryRecord(Map<String, dynamic> record) async {
    final allCattle = await _databaseHelper.cattleHandler.getAllCattle();

    if (!mounted) return;
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => DeliveryFormDialog(
        record: record,
        existingCattle: allCattle,
      ),
    );

    if (result != null) {
      try {
        // Handle the result from the dialog
        await _databaseHelper.breedingHandler.updateDeliveryRecord(
          result['updatedRecord']['id'],
          result['updatedRecord'],
        );

        if (mounted) {
          final message = BreedingMessageUtils.deliveryRecordUpdated();
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context,
              'Error updating delivery record: ${_getReadableErrorMessage(e)}');
        }
      }
    }
  }

  // Handle delete record
  Future<void> _deleteDeliveryRecord(Map<String, dynamic> record) async {
    // Find linked records that will be cascade deleted
    List<String> cascadeWarnings = [];

    try {
      // Check if this delivery record has a linked pregnancy
      if (record['pregnancyId'] != null) {
        final pregnancyId = record['pregnancyId'];
        cascadeWarnings.add('1 pregnancy record');
        cascadeWarnings.add(pregnancyId);

        // Get the pregnancy record to find linked breeding record
        final pregnancyRecords = await _databaseHelper.breedingHandler
            .getPregnancyRecordsForCattle(record['cattleId']);

        final linkedPregnancy = pregnancyRecords.firstWhere(
          (pr) => pr.businessId == pregnancyId,
          orElse: () => PregnancyRecordIsar(),
        );

        if (linkedPregnancy.breedingRecordId != null) {
          cascadeWarnings.add('1 breeding record');
          cascadeWarnings.add(linkedPregnancy.breedingRecordId!);
        }
      }
    } catch (e) {
      debugPrint('Error finding linked records: $e');
      // Continue with deletion even if we can't find linked records
    }

    // Show standardized confirmation dialog with cascade warnings
    final cattle = _cattleMap[record['cattleId']];
    final cattleDisplayName = cattle?.name != null && cattle?.tagId != null
        ? '${cattle!.name} (${cattle.tagId})'
        : cattle?.name ?? cattle?.tagId ?? 'Unknown';
    if (!mounted) return;
    final confirmed = await BreedingMessageUtils.showDeliveryDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: record['id']?.toString(),
      specificRecords: cascadeWarnings,
    );

    if (confirmed == true) {
      try {
        // Use the centralized method to delete with cascade deletion
        await _databaseHelper.breedingHandler.deleteDeliveryRecord(record['id']);

        // Update local state immediately to ensure UI reflects the change
        _safeSetState(() {
          // Remove the record from the list
          _deliveryRecords.removeWhere((r) => r['id'] == record['id']);
          // Re-apply filters
          _filterRecords();
        });

        if (mounted) {
          final message = BreedingMessageUtils.deliveryRecordDeleted();
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context,
              'Error deleting delivery record: ${_getReadableErrorMessage(e)}');
        }
      }
    }
  }

  // Add new delivery record
  Future<void> _addDeliveryRecord() async {
    setState(() => _isLoading = true);

    try {
      // Get eligible cattle (pregnant female cattle)
      final eligibleCattle = await _getEligibleCattleForDelivery();

      setState(() => _isLoading = false);

      if (eligibleCattle.isEmpty) {
        // Show message if no eligible cattle
        if (mounted && context.mounted) {
          BreedingMessageUtils.showWarning(context,
              'No pregnant cattle found. Only pregnant cattle can have delivery records.');
        }
        return;
      }

      if (!mounted) return;

      // Show delivery form dialog
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      if (!mounted) return;
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => DeliveryFormDialog(
          existingCattle: allCattle,
        ),
      );

      // Handle form result
      if (result != null && mounted) {
        await _handleNewDeliveryRecord(result);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, BreedingMessageUtils.generalError(e.toString()));
      }
    }
  }

  // Get eligible cattle for delivery (pregnant cattle)
  Future<List<CattleIsar>> _getEligibleCattleForDelivery() async {
    final allCattle = _cattleMap.values.toList();
    final eligibleCattle = <CattleIsar>[];

    for (final cattle in allCattle) {
      // Only check female cattle that are pregnant
      if (cattle.gender?.toLowerCase() == 'female' &&
          cattle.breedingStatus?.isPregnant == true) {
        eligibleCattle.add(cattle);
      }
    }

    return eligibleCattle;
  }

  // Handle new delivery record creation
  Future<void> _handleNewDeliveryRecord(Map<String, dynamic> result) async {
    try {
      // Use the breeding handler to add the delivery record
      await _databaseHelper.breedingHandler.addDeliveryRecordFromMap(result);

      if (mounted) {
        final message = BreedingMessageUtils.deliveryRecordCreated();
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context,
            'Error creating delivery record: ${_getReadableErrorMessage(e)}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Delivery Records',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addDeliveryRecord,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Reusable Filter and Search Component
          ReusableFilterSearch(
            config: FilterConfig.breeding,
            filterState: _filterState,
            filterData: FilterData(
              cattleMap: _cattleMap,
              animalTypes: _animalTypes,
              animalTypeIdToName: _animalTypeIdToName,
              filteredCattle: _filteredCattle,
            ),
            searchController: _searchController,
            onFilterChanged: _onFilterChanged,
            onSearchChanged: _onSearchChanged,
            onClearFilters: _onClearFilters,
            totalRecords: _deliveryRecords.length,
            filteredRecords: _filteredRecords.length,
          ),

          // Records list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredRecords.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.child_care_outlined,
                              size: 80,
                              color: AppColors.primary.withValues(alpha: 0.4),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _filterState.searchQuery.isEmpty
                                  ? 'No delivery records found'
                                  : 'No matching records found',
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.primary.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _refreshRecords,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              _buildRecordsList(),
                              // Add padding at the bottom for the FAB
                              const SizedBox(height: 80),
                            ],
                          ),
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  void _navigateToCattleDeliveryTab(String? tagId) async {
    if (tagId == null) return;

    final cattle = _cattleMap[tagId];
    if (cattle == null) {
      if (mounted) {
        BreedingMessageUtils.showError(context, 'Cattle not found');
      }
      return;
    }

    try {
      // Get breed and animal type information
      final breedData = await _databaseHelper.farmSetupHandler.getAllBreedCategories();
      final animalTypeData = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();

      // Find matching breed and animal type for this cattle
      final breed = breedData.firstWhere(
        (b) => b.businessId == cattle.breedId,
        orElse: () => breedData.first,
      );

      final animalType = animalTypeData.firstWhere(
        (t) => t.businessId == cattle.animalTypeId,
        orElse: () => animalTypeData.first,
      );

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CattleDetailScreenWithDeliveryTab(
              cattle: cattle,
              breed: breed,
              animalType: animalType,
              onCattleUpdated: (updatedCattle) async {
                await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
                _refreshRecords();
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context, 'Error: $e');
      }
    }
  }



  Widget _buildRecordsList() {
    // Prepare records with cattle name and ID info for display
    final recordsForDisplay = _filteredRecords.map((record) {
      final cattle = _cattleMap[record['cattleId']];
      return {
        ...record,
        'cattleName': cattle?.name ?? 'Unknown Cattle',
        'cattleId': cattle?.tagId ?? 'Unknown',  // Add cattle tag ID for display
      };
    }).toList();

    return DeliveryHistoryCard(
      records: recordsForDisplay,
      title: 'Delivery Records',
      emptyMessage: 'No delivery records found',
      onEdit: _editDeliveryRecord,
      onDelete: _deleteDeliveryRecord,
      onCattleTap: (record) {
        // Navigate to cattle detail screen with delivery tab
        _navigateToCattleDeliveryTab(record['cattleId']);
      },
    );
  }

}
