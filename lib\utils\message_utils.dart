import 'package:flutter/material.dart';

/// Utility class for standardized messaging across the entire application
/// 
/// This class provides consistent success, error, warning, and info messages
/// with standardized styling, durations, and user experience patterns.
/// 
/// Features:
/// - Consistent visual styling (colors, durations, positioning)
/// - Standardized message formats for CRUD operations
/// - Professional delete confirmation dialogs
/// - Comprehensive validation messages
/// - Extensible architecture for new message types
/// - Support for all application modules (Breeding, Cattle, Farm Setup, etc.)
class MessageUtils {
  // Standard colors
  static const Color successColor = Color(0xFF2E7D32);
  static const Color errorColor = Colors.red;
  static const Color warningColor = Color(0xFF2E7D32);
  static const Color infoColor = Color(0xFF2196F3);
  static const Color mutedTextColor = Color(0xFF2E7D32);

  // Standard durations
  static const Duration defaultDuration = Duration(seconds: 4);
  static const Duration shortDuration = Duration(seconds: 2);
  static const Duration longDuration = Duration(seconds: 6);

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: successColor,
        duration: duration ?? defaultDuration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: errorColor,
        duration: duration ?? defaultDuration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: warningColor,
        duration: duration ?? defaultDuration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: infoColor,
        duration: duration ?? defaultDuration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  // ===== GENERAL CRUD MESSAGES =====

  /// Generic CRUD operation messages
  static String recordCreated(String recordType) => '$recordType created successfully';
  static String recordUpdated(String recordType) => '$recordType updated successfully';
  static String recordDeleted(String recordType) => '$recordType deleted successfully';
  static String statusUpdated(String newStatus) => 'Status updated to "$newStatus"';

  // ===== ERROR MESSAGES =====

  /// Standard error messages
  static String get invalidRecordId => 'Invalid record ID';
  static String get recordIdMissing => 'Record ID is missing';
  static String get noDataFound => 'No data found';

  /// Operation error messages
  static String errorLoadingData(String details) => 'Error loading data: $details';
  static String errorRefreshingData(String details) => 'Error refreshing data: $details';
  static String errorUpdatingRecord(String details) => 'Error updating record: $details';
  static String errorDeletingRecord(String details) => 'Error deleting record: $details';
  static String errorAddingRecord(String details) => 'Error adding record: $details';
  static String errorUpdatingStatus(String details) => 'Error updating status: $details';
  static String generalError(String details) => 'Error: $details';

  // ===== DELETE CONFIRMATION DIALOGS =====

  /// Show standardized delete confirmation dialog
  static Future<bool?> showDeleteConfirmation(
    BuildContext context, {
    required String recordType,
    String? itemName,
    String? recordId,
    List<String> relatedRecords = const [],
    String? customWarning,
  }) async {
    if (!context.mounted) return false;

    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Confirm Deletion'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete this $recordType?',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            if (itemName != null) ...[
              const SizedBox(height: 8),
              Text('Name: $itemName'),
            ],
            if (recordId != null) ...[
              const SizedBox(height: 4),
              Text('ID: $recordId'),
            ],
            const SizedBox(height: 12),
            const Text(
              'This action cannot be undone.',
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: mutedTextColor,
              ),
            ),
            if (relatedRecords.isNotEmpty || customWarning != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Warning: This will also delete:',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              if (customWarning != null)
                Padding(
                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.circle, size: 6, color: Colors.red),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          customWarning,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
              ...relatedRecords.map((record) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  children: [
                    const Icon(Icons.circle, size: 6, color: Colors.red),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        record,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
              backgroundColor: Colors.red.withValues(alpha: 0.1),
            ),
            child: const Text(
              'Delete',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  // ===== UTILITY METHODS =====

  /// Show success with automatic message generation
  static void showOperationSuccess(
    BuildContext context,
    String operation,
    String recordType, {
    String? customMessage,
  }) {
    String message = customMessage ?? '';
    
    if (customMessage == null) {
      switch (operation.toLowerCase()) {
        case 'created':
        case 'added':
          message = recordCreated(recordType);
          break;
        case 'updated':
          message = recordUpdated(recordType);
          break;
        case 'deleted':
          message = recordDeleted(recordType);
          break;
        default:
          message = 'Operation completed successfully';
      }
    }
    
    showSuccess(context, message);
  }

  /// Show error with automatic message generation
  static void showOperationError(
    BuildContext context,
    String operation,
    String details, {
    String? customMessage,
  }) {
    String message = customMessage ?? '';
    
    if (customMessage == null) {
      switch (operation.toLowerCase()) {
        case 'loading':
          message = errorLoadingData(details);
          break;
        case 'refreshing':
          message = errorRefreshingData(details);
          break;
        case 'updating':
          message = errorUpdatingRecord(details);
          break;
        case 'deleting':
          message = errorDeletingRecord(details);
          break;
        case 'adding':
        case 'creating':
          message = errorAddingRecord(details);
          break;
        case 'status':
          message = errorUpdatingStatus(details);
          break;
        default:
          message = generalError(details);
      }
    }
    
    showError(context, message);
  }
}

// ===== MODULE-SPECIFIC MESSAGE UTILITIES =====

/// Breeding module specific messages
class BreedingMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== BREEDING RECORD MESSAGES =====

  /// Breeding record creation messages
  static String breedingRecordCreated({bool withPregnancy = false}) {
    return withPregnancy
        ? 'Breeding record created successfully with pregnancy record'
        : 'Breeding record created successfully';
  }

  /// Breeding record update messages
  static String breedingRecordUpdated({
    bool pregnancyCreated = false,
    bool pregnancyUpdated = false,
  }) {
    String message = 'Breeding record updated successfully';
    if (pregnancyCreated) {
      message += ' with pregnancy record created';
    } else if (pregnancyUpdated) {
      message += ' with pregnancy record updated';
    }
    return message;
  }

  /// Breeding status update messages
  static String breedingStatusUpdated(
    String newStatus, {
    bool pregnancyCreated = false,
    bool pregnancyUpdated = false,
  }) {
    String message = 'Breeding status updated to "$newStatus"';
    if (pregnancyCreated) {
      message += ' with pregnancy record created';
    } else if (pregnancyUpdated) {
      message += ' with pregnancy record updated';
    }
    return message;
  }

  /// Breeding record deletion messages
  static String breedingRecordDeleted({
    int pregnancyRecords = 0,
    int deliveryRecords = 0,
    List<String> specificRecords = const [],
  }) {
    String message = 'Breeding record deleted successfully';

    if (pregnancyRecords > 0 || deliveryRecords > 0 || specificRecords.isNotEmpty) {
      message += ' with related records:';
      if (pregnancyRecords > 0) {
        message += '\n• $pregnancyRecords pregnancy ${pregnancyRecords == 1 ? 'record' : 'records'}';
      }
      if (deliveryRecords > 0) {
        message += '\n• $deliveryRecords delivery ${deliveryRecords == 1 ? 'record' : 'records'}';
      }
      for (final record in specificRecords) {
        message += '\n• $record';
      }
    }

    return message;
  }

  // ===== PREGNANCY RECORD MESSAGES =====

  /// Pregnancy record creation messages
  static String pregnancyRecordCreated({bool withDelivery = false}) {
    return withDelivery
        ? 'Pregnancy record created successfully with delivery record'
        : 'Pregnancy record created successfully';
  }

  /// Pregnancy record update messages
  static String pregnancyRecordUpdated({
    bool deliveryCreated = false,
    bool deliveryUpdated = false,
  }) {
    String message = 'Pregnancy record updated successfully';
    if (deliveryCreated) {
      message += ' with delivery record created';
    } else if (deliveryUpdated) {
      message += ' with delivery record updated';
    }
    return message;
  }

  /// Pregnancy record deletion messages
  static String pregnancyRecordDeleted({
    int deliveryRecords = 0,
    List<String> specificRecords = const [],
  }) {
    String message = 'Pregnancy record deleted successfully';

    if (deliveryRecords > 0 || specificRecords.isNotEmpty) {
      message += ' with related records:';
      if (deliveryRecords > 0) {
        message += '\n• $deliveryRecords delivery ${deliveryRecords == 1 ? 'record' : 'records'}';
      }
      for (final record in specificRecords) {
        message += '\n• $record';
      }
    }

    return message;
  }

  // ===== DELIVERY RECORD MESSAGES =====

  /// Delivery record messages
  static String deliveryRecordCreated() => 'Delivery record created successfully';
  static String deliveryRecordUpdated() => 'Delivery record updated successfully';
  static String deliveryRecordDeleted() => 'Delivery record deleted successfully';

  // ===== BREEDING-SPECIFIC VALIDATION MESSAGES =====

  /// Eligibility messages
  static String get noEligibleCattle => 'No eligible cattle found for breeding';
  static String get noFemaleCattleFound => 'No female cattle found';
  static String get cattleIdMissing => 'Cattle ID is missing';
  static String get breedingRecordIdMissing => 'Breeding record ID is missing';
  static String get invalidRecordId => 'Invalid record ID';

  static String eligibilityError(String cattleName, String reason) =>
      'Cannot breed $cattleName: $reason';

  static String generalError(String details) => 'Error: $details';

  /// Show breeding-specific error message
  static void showBreedingError(
    BuildContext context,
    String operation,
    String details, {
    Duration? duration,
  }) {
    MessageUtils.showOperationError(context, operation, details);
  }

  // ===== BREEDING-SPECIFIC DELETE CONFIRMATIONS =====

  /// Show standardized delete confirmation for breeding records
  static Future<bool?> showBreedingDeleteConfirmation(
    BuildContext context, {
    String? cattleName,
    String? recordId,
    int pregnancyRecords = 0,
    int deliveryRecords = 0,
    List<String> specificRecords = const [],
  }) async {
    List<String> warnings = [];
    if (pregnancyRecords > 0) {
      warnings.add('$pregnancyRecords pregnancy ${pregnancyRecords == 1 ? 'record' : 'records'}');
    }
    if (deliveryRecords > 0) {
      warnings.add('$deliveryRecords delivery ${deliveryRecords == 1 ? 'record' : 'records'}');
    }
    warnings.addAll(specificRecords);

    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'breeding record',
      itemName: cattleName,
      recordId: recordId,
      relatedRecords: warnings,
    );
  }

  /// Show standardized delete confirmation for pregnancy records
  static Future<bool?> showPregnancyDeleteConfirmation(
    BuildContext context, {
    String? cattleName,
    String? recordId,
    int deliveryRecords = 0,
    int breedingRecords = 0,
    List<String> specificRecords = const [],
  }) async {
    List<String> warnings = [];
    if (deliveryRecords > 0) {
      warnings.add('$deliveryRecords delivery ${deliveryRecords == 1 ? 'record' : 'records'}');
    }
    if (breedingRecords > 0) {
      warnings.add('$breedingRecords breeding ${breedingRecords == 1 ? 'record' : 'records'}');
    }
    warnings.addAll(specificRecords);

    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'pregnancy record',
      itemName: cattleName,
      recordId: recordId,
      relatedRecords: warnings,
    );
  }

  /// Show standardized delete confirmation for delivery records
  static Future<bool?> showDeliveryDeleteConfirmation(
    BuildContext context, {
    String? cattleName,
    String? recordId,
    List<String> specificRecords = const [],
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'delivery record',
      itemName: cattleName,
      recordId: recordId,
      relatedRecords: specificRecords,
    );
  }
}

/// Cattle module specific messages
class CattleMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== CATTLE RECORD MESSAGES =====

  static String cattleRecordCreated() => 'Cattle record created successfully';
  static String cattleRecordUpdated() => 'Cattle record updated successfully';
  static String cattleRecordDeleted() => 'Cattle record deleted successfully';

  // ===== CATTLE-SPECIFIC VALIDATION MESSAGES =====

  static String get tagIdRequired => 'Tag ID is required';
  static String get nameRequired => 'Cattle name is required';
  static String get animalTypeRequired => 'Animal type is required';
  static String get genderRequired => 'Gender is required';
  static String get duplicateTagId => 'Tag ID already exists';

  static String tagIdExists(String tagId) => 'Tag ID "$tagId" already exists';

  // ===== CATTLE DELETE CONFIRMATIONS =====

  static Future<bool?> showCattleDeleteConfirmation(
    BuildContext context, {
    String? cattleName,
    String? tagId,
    int breedingRecords = 0,
    int pregnancyRecords = 0,
    int deliveryRecords = 0,
    List<String> specificRecords = const [],
  }) async {
    List<String> warnings = [];
    if (breedingRecords > 0) {
      warnings.add('$breedingRecords breeding ${breedingRecords == 1 ? 'record' : 'records'}');
    }
    if (pregnancyRecords > 0) {
      warnings.add('$pregnancyRecords pregnancy ${pregnancyRecords == 1 ? 'record' : 'records'}');
    }
    if (deliveryRecords > 0) {
      warnings.add('$deliveryRecords delivery ${deliveryRecords == 1 ? 'record' : 'records'}');
    }
    warnings.addAll(specificRecords);

    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'cattle record',
      itemName: cattleName,
      recordId: tagId,
      relatedRecords: warnings,
    );
  }
}

/// Farm Setup module specific messages
class FarmSetupMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== ANIMAL TYPE MESSAGES =====

  static String animalTypeCreated() => 'Animal type created successfully';
  static String animalTypeUpdated() => 'Animal type updated successfully';
  static String animalTypeDeleted() => 'Animal type deleted successfully';

  // ===== FARM SETUP VALIDATION MESSAGES =====

  static String get animalTypeNameRequired => 'Animal type name is required';
  static String get gestationDaysRequired => 'Gestation days is required';
  static String get duplicateAnimalTypeName => 'Animal type name already exists';

  static String animalTypeNameExists(String name) => 'Animal type "$name" already exists';

  // ===== FARM SETUP DELETE CONFIRMATIONS =====

  static Future<bool?> showAnimalTypeDeleteConfirmation(
    BuildContext context, {
    String? animalTypeName,
    String? animalTypeId,
    int cattleCount = 0,
    List<String> specificRecords = const [],
  }) async {
    List<String> warnings = [];
    if (cattleCount > 0) {
      warnings.add('$cattleCount cattle ${cattleCount == 1 ? 'record' : 'records'} using this type');
    }
    warnings.addAll(specificRecords);

    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'animal type',
      itemName: animalTypeName,
      recordId: animalTypeId,
      relatedRecords: warnings,
    );
  }
}

/// Health module specific messages
class HealthMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== HEALTH RECORD MESSAGES =====

  static String healthRecordCreated() => 'Health record created successfully';
  static String healthRecordUpdated() => 'Health record updated successfully';
  static String healthRecordDeleted() => 'Health record deleted successfully';

  static String vaccinationRecorded() => 'Vaccination recorded successfully';
  static String treatmentRecorded() => 'Treatment recorded successfully';
  static String checkupRecorded() => 'Health checkup recorded successfully';

  // ===== HEALTH-SPECIFIC VALIDATION MESSAGES =====

  static String get healthTypeRequired => 'Health record type is required';
  static String get dateRequired => 'Date is required';
  static String get veterinarianRequired => 'Veterinarian information is required';

  // ===== HEALTH DELETE CONFIRMATIONS =====

  static Future<bool?> showHealthRecordDeleteConfirmation(
    BuildContext context, {
    String? cattleName,
    String? recordId,
    String? recordType,
    List<String> specificRecords = const [],
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: recordType ?? 'health record',
      itemName: cattleName,
      recordId: recordId,
      relatedRecords: specificRecords,
    );
  }
}

/// Financial module specific messages
class FinancialMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== FINANCIAL RECORD MESSAGES =====

  static String expenseRecorded() => 'Expense recorded successfully';
  static String incomeRecorded() => 'Income recorded successfully';
  static String transactionUpdated() => 'Transaction updated successfully';
  static String transactionDeleted() => 'Transaction deleted successfully';

  // ===== FINANCIAL-SPECIFIC VALIDATION MESSAGES =====

  static String get amountRequired => 'Amount is required';
  static String get categoryRequired => 'Category is required';
  static String get invalidAmount => 'Please enter a valid amount';

  // ===== FINANCIAL DELETE CONFIRMATIONS =====

  static Future<bool?> showTransactionDeleteConfirmation(
    BuildContext context, {
    String? description,
    String? transactionId,
    String? amount,
    List<String> specificRecords = const [],
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'transaction',
      itemName: description,
      recordId: transactionId,
      relatedRecords: specificRecords,
      customWarning: amount != null ? 'Amount: $amount' : null,
    );
  }
}

/// Milk Records module specific messages
class MilkMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== MILK RECORD MESSAGES =====

  static String milkRecordCreated() => 'Milk record created successfully';
  static String milkRecordUpdated() => 'Milk record updated successfully';
  static String milkRecordDeleted() => 'Milk record deleted successfully';

  static String milkSaleRecorded() => 'Milk sale recorded successfully';
  static String milkSaleUpdated() => 'Milk sale updated successfully';
  static String milkSaleDeleted() => 'Milk sale deleted successfully';

  // ===== MILK-SPECIFIC VALIDATION MESSAGES =====

  static String get quantityRequired => 'Quantity is required';
  static String get invalidQuantity => 'Please enter a valid quantity';
  static String get dateRequired => 'Date is required';
  static String get cattleRequired => 'Please select cattle';

  // ===== MILK DELETE CONFIRMATIONS =====

  static Future<bool?> showMilkRecordDeleteConfirmation(
    BuildContext context, {
    String? cattleName,
    String? recordId,
    String? date,
    List<String> specificRecords = const [],
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'milk record',
      itemName: cattleName,
      recordId: recordId,
      relatedRecords: specificRecords,
      customWarning: date != null ? 'Date: $date' : null,
    );
  }

  static Future<bool?> showMilkSaleDeleteConfirmation(
    BuildContext context, {
    String? buyer,
    String? recordId,
    String? amount,
    List<String> specificRecords = const [],
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'milk sale',
      itemName: buyer,
      recordId: recordId,
      relatedRecords: specificRecords,
      customWarning: amount != null ? 'Amount: $amount' : null,
    );
  }
}

/// Events module specific messages
class EventsMessageUtils extends MessageUtils {
  // ===== EVENT RECORD MESSAGES =====

  static String eventCreated() => 'Event created successfully';
  static String eventUpdated() => 'Event updated successfully';
  static String eventDeleted() => 'Event deleted successfully';
  static String eventCompleted() => 'Event marked as completed';
  static String eventRescheduled() => 'Event rescheduled successfully';

  // ===== EVENTS-SPECIFIC VALIDATION MESSAGES =====

  static String get titleRequired => 'Event title is required';
  static String get dateRequired => 'Event date is required';
  static String get typeRequired => 'Event type is required';
  static String get cattleRequired => 'Please select cattle';

  // ===== EVENTS DELETE CONFIRMATIONS =====

  static Future<bool?> showEventDeleteConfirmation(
    BuildContext context, {
    String? eventTitle,
    String? eventId,
    String? eventDate,
    List<String> specificRecords = const [],
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'event',
      itemName: eventTitle,
      recordId: eventId,
      relatedRecords: specificRecords,
      customWarning: eventDate != null ? 'Date: $eventDate' : null,
    );
  }
}
