import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../Breeding/services/breeding_handler.dart';
import '../../Milk Records/services/milk_handler.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../cattle_tabs/overview_tab.dart';
import '../cattle_tabs/family_tree_tab.dart';
import '../cattle_tabs/events_tab.dart';
import '../cattle_tabs/health_tab.dart';
import '../cattle_tabs/milk_tab.dart';
import '../../../utils/message_utils.dart';
import '../cattle_tabs/breeding_tab.dart';

class CattleDetailScreen extends StatefulWidget {
  final CattleIsar existingCattle;
  final String businessId;
  final Function(CattleIsar) onCattleUpdated;
  final int? initialTabIndex; // Add parameter for initial tab selection
  final bool? milkRecordsSelected; // Add parameter for milk records sub-tab selection
  final int? initialBreedingTabIndex; // Add parameter for breeding sub-tab selection
  final int? initialHealthTabIndex; // Add parameter for health sub-tab selection

  const CattleDetailScreen({
    Key? key,
    required this.existingCattle,
    required this.businessId,
    required this.onCattleUpdated,
    this.initialTabIndex, // Optional initial tab index
    this.milkRecordsSelected, // Optional milk records sub-tab selection
    this.initialBreedingTabIndex, // Optional breeding sub-tab selection
    this.initialHealthTabIndex, // Optional health sub-tab selection
  }) : super(key: key);

  @override
  State<CattleDetailScreen> createState() => _CattleDetailScreenState();
}

class _CattleDetailScreenState extends State<CattleDetailScreen>
    with SingleTickerProviderStateMixin {
  late CattleIsar _cattle;
  CattleIsar? motherCattle;
  List<CattleIsar> offspring = [];
  List<CattleIsar> siblings = [];
  List<CattleIsar> allCattle = []; // Added for enhanced family tree functionality
  late List<AnimalTypeIsar> _animalTypes = [];
  late List<BreedCategoryIsar> _breeds = [];
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      debugPrint('DEBUG: CattleDetailScreen.initState called');
      debugPrint('DEBUG: businessId = ${widget.businessId}');
      debugPrint('DEBUG: existingCattle.name = ${widget.existingCattle.name}');
      debugPrint(
          'DEBUG: existingCattle.businessId = ${widget.existingCattle.businessId}');
    }

    _cattle = widget.existingCattle;

    // Calculate tab count based on gender - females have Breeding and Milk tabs
    final isFemale = (_cattle.gender?.toLowerCase() ?? '') == 'female';

    // Determine initial tab index
    int initialIndex = 0; // Default to Overview tab
    if (widget.initialTabIndex != null) {
      final maxIndex = isFemale ? 5 : 3; // Max valid index based on gender
      initialIndex = widget.initialTabIndex!.clamp(0, maxIndex);
    }

    _tabController = TabController(
      length: isFemale ? 6 : 4, // 6 tabs for female, 4 for male
      vsync: this,
      initialIndex: initialIndex, // Set initial tab
    );

    // Delayed loading to ensure widget is fully initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadData();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Handle cattle updates from child components
  void _handleCattleUpdate(CattleIsar updatedCattle) {
    if (mounted) {
      setState(() {
        _cattle = updatedCattle;
      });
      // Pass the update to the parent component
      widget.onCattleUpdated(updatedCattle);
    }
  }

  Future<void> _loadData() async {
    if (!mounted) {
      if (kDebugMode) {
        debugPrint('DEBUG: _loadData aborted, widget not mounted');
      }
      return;
    }

    if (kDebugMode) {
      debugPrint('DEBUG: CattleDetailScreen._loadData started');
      debugPrint(
          'DEBUG: Loading details for cattle with businessId: ${_cattle.businessId}');
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final cattleHandler = GetIt.instance<CattleHandler>();
      final farmSetupHandler = GetIt.instance<FarmSetupHandler>();

      debugPrint('DEBUG: Getting cattle by ID: ${_cattle.businessId}');

      // Load core data concurrently
      final results = await Future.wait([
        cattleHandler.getCattleById(_cattle.businessId ?? ''),
        farmSetupHandler.getAllAnimalTypes(),
        cattleHandler.getAllCattle(), // Added to get all cattle for enhanced family tree
      ]);

      final updatedCattle = results[0] as CattleIsar?;
      final allAnimalTypes = results[1] as List<AnimalTypeIsar>;
      final loadedAllCattle = results[2] as List<CattleIsar>;

      debugPrint('DEBUG: Data loaded. Found cattle: ${updatedCattle != null}');
      debugPrint('DEBUG: Loaded ${allAnimalTypes.length} animal types');

      if (!mounted) {
        debugPrint('DEBUG: Widget no longer mounted after data load');
        return;
      }

      // Update state with the results
      setState(() {
        if (updatedCattle != null) {
          _cattle = updatedCattle;
          debugPrint('DEBUG: Updated _cattle with data from database');
        } else {
          debugPrint(
              'DEBUG: No cattle found with businessId ${_cattle.businessId}, using existingCattle');
        }
        _animalTypes = allAnimalTypes;
        allCattle = loadedAllCattle; // Store all cattle for enhanced family tree
        debugPrint('DEBUG: Loaded ${allCattle.length} total cattle records');
      });

      // Load mother, offspring, and potential siblings concurrently
      List<CattleIsar> loadedOffspring = [];
      List<CattleIsar> potentialSiblings = [];
      CattleIsar? loadedMother;

      // Define futures for relationship data
      Future<CattleIsar?> getMotherFuture = _cattle.motherTagId?.isNotEmpty == true
          ? cattleHandler.getCattleByTagId(_cattle.motherTagId!)
          : Future.value(null);

      Future<List<CattleIsar>> getCalvesFuture = _cattle.tagId?.isNotEmpty == true
          ? cattleHandler.getCattleByMotherTagId(_cattle.tagId!)
          : Future.value([]);

      Future<List<CattleIsar>> getPotentialSiblingsFuture = _cattle.motherTagId?.isNotEmpty == true
          ? cattleHandler.getCattleByMotherTagId(_cattle.motherTagId!)
          : Future.value([]);


      final relationshipResults = await Future.wait([
        getMotherFuture,
        getCalvesFuture,
        getPotentialSiblingsFuture,
      ]);

      loadedMother = relationshipResults[0] as CattleIsar?;
      loadedOffspring = relationshipResults[1] as List<CattleIsar>;
      potentialSiblings = relationshipResults[2] as List<CattleIsar>;

      // Calculate actual siblings by filtering out the current cattle
      final calculatedSiblings = potentialSiblings
          .where((s) => s.businessId != _cattle.businessId)
          .toList();

      if (!mounted) return;

      setState(() {
        motherCattle = loadedMother;
        offspring = loadedOffspring;
        siblings = calculatedSiblings;

        debugPrint('DEBUG: Found mother cattle: ${motherCattle?.name}');
        debugPrint('DEBUG: Found ${offspring.length} offspring');
        debugPrint('DEBUG: Found ${siblings.length} siblings');
      });

      // Load breed data after we have the animal type
      if (_cattle.animalTypeId != null && _cattle.animalTypeId!.isNotEmpty) {
        try {
          debugPrint(
              'DEBUG: Loading breeds for animal type: ${_cattle.animalTypeId}');
          final specificBreeds = await farmSetupHandler
              .getBreedCategoriesForAnimalType(_cattle.animalTypeId!);

          if (!mounted) return;

          setState(() {
            _breeds = specificBreeds.isNotEmpty
                ? specificBreeds
                : []; // Empty list if no breeds found
            debugPrint('DEBUG: Loaded ${_breeds.length} specific breeds');
          });
        } catch (e, s) {
          // Correctly place catch after the inner try block
          debugPrint(
              'Error loading breeds for animal type ${_cattle.animalTypeId}: $e\n$s');

          if (!mounted) return;

          // Fall back to all breeds only if there was an error
          final allBreeds = await farmSetupHandler.getAllBreedCategories();

          if (!mounted) return;

          setState(() {
            _breeds = allBreeds;
          });

          // Only show error if we're still mounted
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                  'Error loading breed data for this animal type. Showing all breeds.'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      } else {
        // No animal type, load all breeds
        final allBreeds = await farmSetupHandler.getAllBreedCategories();

        if (!mounted) return;

        setState(() {
          _breeds = allBreeds;
        });
      }
    } catch (e, s) {
      debugPrint('ERROR: Error loading cattle detail data: $e');
      debugPrint('ERROR: Stack trace: $s');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading data: ${_getFriendlyErrorMessage(e)}'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          debugPrint('DEBUG: Data loading complete, set _isLoading to false');
        });
      } else {
        debugPrint('DEBUG: Widget unmounted during data loading');
      }
    }
  }

  void _showEditDialog() {
    // Capture the current scaffold context before showing dialog
    final scaffoldContext = context;

    showDialog(
      context: context,
      builder: (dialogContext) => CattleFormDialog(
        cattle: _cattle,
        existingCattle:
            siblings, // Pass the full cattle list for uniqueness checks
        businessId: widget.businessId,
        animalTypes: _animalTypes,
        onSave: (updatedCattle) async {
          // Capture context-dependent variables before async gaps
          final scaffoldMessenger = ScaffoldMessenger.of(scaffoldContext);
          final navigator =
              Navigator.of(dialogContext); // Capture navigator too
          final currentTheme = Theme.of(scaffoldContext); // Capture theme

          try {
            // Close the dialog first to avoid context errors
            navigator.pop(); // Use captured navigator

            // Show loading indicator
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                // Use captured messenger
                const SnackBar(
                  content: Row(
                    children: [
                      SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          )),
                      SizedBox(width: 16),
                      Text('Updating cattle...'),
                    ],
                  ),
                  duration: Duration(seconds: 1),
                ),
              );
            }

            // Get cattle handler
            final cattleHandler = GetIt.instance<CattleHandler>();

            // Process update in a delayed future to allow UI to update
            // await Future.delayed(const Duration(milliseconds: 100));

            // Explicitly call updateCattle method
            await cattleHandler.updateCattle(updatedCattle);

            // Check if widget is still mounted before updating state
            if (!mounted) return;

            // Update UI state after successful save
            setState(() {
              _cattle = updatedCattle;
            });

            // Notify parent component
            widget.onCattleUpdated(updatedCattle);

            // Only reload relationships if tag ID or mother tag ID changed, which affects relationships
            final tagIdChanged = updatedCattle.tagId != _cattle.tagId;
            final motherTagIdChanged =
                updatedCattle.motherTagId != _cattle.motherTagId;

            if (tagIdChanged || motherTagIdChanged) {
              // Only reload specific relationships data, not everything
              try {
                // Reload only what's needed based on what changed
                if (tagIdChanged) {
                  // If tagId changed, we need to reload offspring with the new tagId
                  final newOffspring = await cattleHandler
                      .getCattleByMotherTagId(updatedCattle.tagId ?? '');
                  if (mounted) {
                    setState(() {
                      offspring = newOffspring;
                      debugPrint(
                          'DEBUG: Updated offspring for new tagId: ${updatedCattle.tagId}');
                    });
                  }
                }

                if (motherTagIdChanged) {
                  // If motherTagId changed, we need to reload the mother cattle
                  final newMother =
                      updatedCattle.motherTagId?.isNotEmpty == true
                          ? await cattleHandler
                              .getCattleByTagId(updatedCattle.motherTagId!)
                          : null;
                  if (mounted) {
                    setState(() {
                      motherCattle = newMother;
                      debugPrint(
                          'DEBUG: Updated mother for new motherTagId: ${updatedCattle.motherTagId}');
                    });
                  }
                }
              } catch (e) {
                debugPrint(
                    'ERROR: Error updating relationships after edit: $e');
              }
            }

            // Check mounted again before showing success message
            if (!mounted) return;

            CattleMessageUtils.showSuccess(context,
                CattleMessageUtils.cattleRecordUpdated());
          } catch (e, s) {
            debugPrint('ERROR: Failed to update cattle: $e\n$s');

            // Check if widget is still mounted before showing error
            if (!mounted) return;

            scaffoldMessenger.showSnackBar(
              // Use captured messenger
              SnackBar(
                content: Text(
                    'Failed to update cattle: ${_getFriendlyErrorMessage(e)}'),
                backgroundColor:
                    currentTheme.colorScheme.error, // Use captured theme
              ),
            );
          }
        },
      ),
    );
  }

  // Helper method to convert exceptions to user-friendly messages
  String _getFriendlyErrorMessage(Object e) {
    // Provide specific error messages based on exception type
    if (e.toString().contains('ValidationException')) {
      return e.toString().replaceAll('ValidationException: ', '');
    } else if (e.toString().contains('RecordNotFoundException')) {
      return 'Cattle record not found. It may have been deleted.';
    } else if (e.toString().contains('DatabaseException')) {
      return 'Database error. Please try again later.';
    } else if (e.toString().contains('TimeoutException')) {
      return 'Operation timed out. Please check your connection and try again.';
    } else if (e.toString().contains('SocketException') ||
        e.toString().contains('Connection refused')) {
      return 'Network error. Please check your connection.';
    }

    // Default message for unknown errors
    return 'An unexpected error occurred. Please try again.';
  }

  @override
  Widget build(BuildContext context) {
    // Find the animal type for the cattle
    final animalType = _animalTypes.firstWhere(
      (type) => type.businessId == _cattle.animalTypeId,
      orElse: () => AnimalTypeIsar()..name = 'Unknown',
    );

    // Find the breed for the cattle
    final breed = _breeds.firstWhere(
      (b) => b.businessId == _cattle.breedId,
      orElse: () => BreedCategoryIsar()..name = 'Unknown',
    );

    final isFemale = (_cattle.gender?.toLowerCase() ?? '') == 'female';

    return Scaffold(
      appBar: AppBar(
        title:
            Text('${_cattle.name ?? 'Cattle'} (${_cattle.tagId ?? 'No Tag'})'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _showEditDialog,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () {
              // Show delete confirmation dialog
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Delete Cattle'),
                  content:
                      Text('Are you sure you want to delete ${_cattle.name}?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () async {
                        try {
                          // Show loading indicator
                          Navigator.pop(context); // Close confirmation dialog

                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Row(
                                  children: [
                                    SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    ),
                                    SizedBox(width: 16),
                                    Text('Deleting cattle...'),
                                  ],
                                ),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }

                          // Get the cattle handler and delete the cattle
                          final cattleHandler = GetIt.instance<CattleHandler>();
                          await cattleHandler
                              .deleteCattle(_cattle.businessId ?? '');

                          if (mounted) {
                            // Show success message
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                      '${_cattle.name} deleted successfully'),
                                  backgroundColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              );
                            }

                            // Return to previous screen
                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                          }
                        } catch (e) {
                          if (mounted && context.mounted) {
                            // Show error message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Failed to delete cattle: ${_getFriendlyErrorMessage(e)}'),
                                backgroundColor:
                                    Theme.of(context).colorScheme.error,
                              ),
                            );
                          }
                        }
                      },
                      child: const Text('Delete'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorWeight: 3.0,
          indicatorColor: Colors.white,
          labelStyle: const TextStyle(fontWeight: FontWeight.bold),
          unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
          tabs: [
            const Tab(icon: Icon(Icons.info_outline), text: 'Overview'),
            const Tab(icon: Icon(Icons.account_tree), text: 'Family Tree'),
            if (isFemale)
              const Tab(icon: Icon(Icons.favorite), text: 'Breeding'),
            const Tab(icon: Icon(Icons.healing), text: 'Health'),
            if (isFemale) const Tab(icon: Icon(Icons.water_drop), text: 'Milk'),
            const Tab(icon: Icon(Icons.event_note), text: 'Events'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // Overview Tab
                OverviewTab(
                  cattle: _cattle,
                  animalType: animalType,
                  breed: breed,
                  onCattleUpdated: _handleCattleUpdate,
                  cattleHandler: GetIt.instance<CattleHandler>(),
                  breedingHandler: GetIt.instance<BreedingHandler>(),
                ),

                // Family Tree Tab
                FamilyTreeTab(
                  cattle: _cattle,
                  motherCattle: motherCattle,
                  calves: offspring,
                  siblings: siblings,
                ),

                // Breeding Tab (Female only)
                if (isFemale)
                  BreedingTab(
                    cattle: _cattle,
                    onCattleUpdated: _handleCattleUpdate,
                    initialBreedingTabIndex: widget.initialBreedingTabIndex ?? 0, // Pass breeding sub-tab index
                  ),

                // Health Tab
                HealthTab(
                  cattle: _cattle,
                  initialTabIndex: widget.initialHealthTabIndex ?? 0, // Pass health sub-tab index
                ),

                // Milk Tab (Female only)
                if (isFemale) MilkTab(
                  cattle: _cattle,
                  milkHandler: GetIt.instance<MilkHandler>(),
                  recordsSelected: widget.milkRecordsSelected ?? false, // Pass records selection
                ),

                // Events Tab
                EventsTab(cattle: _cattle),
              ],
            ),
    );
  }
}
