import 'package:flutter/material.dart';
import 'dart:async';
import '../../models/cattle_isar.dart';
import 'package:cattle_manager/services/database/database_helper.dart';
import '../../../../Dashboard/Breeding/dialogs/delivery_form_dialog.dart';
import '../../widgets/delivery_history_card.dart';
import '../../widgets/status_card.dart';
import '../../widgets/stats_card.dart';
import '../../../../constants/app_colors.dart';
import '../../../../utils/message_utils.dart';
import 'package:get_it/get_it.dart';
import '../../../../services/streams/stream_service.dart';
import '../../../../Dashboard/Breeding/models/pregnancy_record_isar.dart';

// Extension method to add firstWhereOrNull to List
extension ListExtensions<T> on List<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (var element in this) {
      if (test(element)) return element;
    }
    return null;
  }
}

class DeliveryView extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar) onCattleUpdated;

  const DeliveryView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
  });

  @override
  State<DeliveryView> createState() => _DeliveryViewState();
}

class _DeliveryViewState extends State<DeliveryView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  bool _isLoading = true;
  List<Map<String, dynamic>> _deliveryRecords = [];
  List<PregnancyRecordIsar> _pregnancyRecords = [];
  StreamSubscription? _breedingRecordSubscription;
  StreamSubscription? _pregnancyRecordSubscription;
  StreamSubscription? _deliveryRecordSubscription;
  bool _isProcessingState = false;
  Timer? _stateDebounceTimer;
  List<CattleIsar> _allCattle = [];

  @override
  void initState() {
    super.initState();
    _loadDeliveryRecords();
    _subscribeToRecordUpdates();
  }

  @override
  void dispose() {
    _breedingRecordSubscription?.cancel();
    _pregnancyRecordSubscription?.cancel();
    _deliveryRecordSubscription?.cancel();
    _stateDebounceTimer?.cancel();
    super.dispose();
  }

  void _subscribeToRecordUpdates() {
    Timer? debounceTimer;
    Map<String, dynamic>? lastUpdate;

    // Get the stream service
    final streamService = GetIt.instance<StreamService>();

    // Listen to breeding record updates
    _breedingRecordSubscription =
        streamService.breedingStream.listen((event) async {
      _handleRecordUpdate(event, debounceTimer, lastUpdate);
    });

    // Listen to pregnancy record updates
    _pregnancyRecordSubscription =
        streamService.pregnancyStream.listen((event) async {
      _handleRecordUpdate(event, debounceTimer, lastUpdate);
    });

    // Listen to delivery record updates
    _deliveryRecordSubscription =
        streamService.deliveryStream.listen((event) async {
      _handleRecordUpdate(event, debounceTimer, lastUpdate);
    });
  }

  void _handleRecordUpdate(Map<String, dynamic> event, Timer? debounceTimer,
      Map<String, dynamic>? lastUpdate) {
    final eventCattleId = event['cattleId'] as String?;
    final action = event['action'] as String?;
    // Check both 'id' and 'recordId' fields for compatibility
    final recordId = event['id'] as String? ?? event['recordId'] as String?;

    // Check if this event is relevant to our cattle
    if (eventCattleId == widget.cattle.tagId) {
      debugPrint('Record update received: $action for cattle $eventCattleId');

      // Handle delete action immediately
      if (action == 'delete' && recordId != null) {
        debugPrint('Handling delete action for delivery record: $recordId');
        if (mounted) {
          setState(() {
            _isLoading = true;
          });

          // Immediately reload the data
          _loadDeliveryRecords().then((_) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
              // Force rebuild
              setState(() {});
            }
          });
          return;
        }
      }

      // Cancel any pending debounce timer
      debounceTimer?.cancel();
      _stateDebounceTimer?.cancel();

      // Set a new debounce timer for other actions
      _stateDebounceTimer = Timer(const Duration(milliseconds: 500), () async {
        if (!mounted || _isProcessingState) return;

        try {
          _isProcessingState = true;

          // Always fetch fresh cattle data
          final updatedCattle = await _databaseHelper.cattleHandler
              .getCattleByTagId(widget.cattle.tagId ?? '');
          if (!mounted) return;

          if (updatedCattle != null) {
            // Update the widget's cattle data through the callback
            widget.onCattleUpdated(updatedCattle);

            // Force UI refresh with setState
            setState(() {
              // The build method will use the updated cattle data
            });
          }
        } finally {
          _isProcessingState = false;
        }
      });
    }
  }

  @override
  void didUpdateWidget(DeliveryView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Refresh when cattle changes or when important dates change
    if (oldWidget.cattle != widget.cattle ||
        oldWidget.cattle.breedingStatus?.expectedCalvingDate !=
            widget.cattle.breedingStatus?.expectedCalvingDate ||
        oldWidget.cattle.breedingStatus?.breedingDate !=
            widget.cattle.breedingStatus?.breedingDate) {
      setState(() {
        // The build method will use the updated cattle data
      });
    }
  }

  Future<void> _loadDeliveryRecords() async {
    setState(() => _isLoading = true);
    try {
      // Load delivery records
      _deliveryRecords = await _databaseHelper.breedingHandler
          .getDeliveryRecordsForCattle(widget.cattle.tagId ?? '');
      debugPrint('Loaded ${_deliveryRecords.length} delivery records for stats card');

      // Load pregnancy records
      final pregnancyRecords = await _databaseHelper.breedingHandler
          .getPregnancyRecordsForCattle(widget.cattle.tagId ?? '');

      // Log the pregnancy records for debugging
      debugPrint(
          'Loaded ${pregnancyRecords.length} pregnancy records for cattle ${widget.cattle.tagId}');
      for (final record in pregnancyRecords) {
        debugPrint(
            'Pregnancy record: ${record.businessId}, status: ${record.status}, expectedDate: ${record.expectedCalvingDate}');
      }

      // Load all cattle
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();

      // Update state
      if (mounted) {
        setState(() {
          _pregnancyRecords = pregnancyRecords;
          _allCattle = allCattle.cast<CattleIsar>();
        });
      }
    } catch (e) {
      // Handle error
      if (mounted) {
        BreedingMessageUtils.showError(context, 'Error loading delivery records: $e');
      }
      _deliveryRecords = [];
      _pregnancyRecords = [];
    }
    setState(() => _isLoading = false);
  }

  Widget _buildStatsCard() {
    // Calculate statistics from delivery records
    debugPrint('Stats Card: Using ${_deliveryRecords.length} delivery records');
    int totalDeliveries = _deliveryRecords.length;
    // Check for complications field, default to successful if not specified
    int successfulDeliveries = _deliveryRecords.where((r) =>
        r['hadComplications'] != true && r['complications'] != true).length;

    // Calculate total calves and gender breakdown
    int totalCalves = 0;
    int maleCalves = 0;
    int femaleCalves = 0;

    for (var record in _deliveryRecords) {
      if (record['calfDetails'] != null) {
        List<dynamic> calfDetails = record['calfDetails'];
        totalCalves += calfDetails.length;

        for (var calf in calfDetails) {
          if (calf['gender'] == 'Male') {
            maleCalves++;
          } else if (calf['gender'] == 'Female') {
            femaleCalves++;
          }
        }
      } else {
        // If calfDetails is not available, use liveCalves count
        totalCalves += (record['liveCalves'] as int? ?? 0);
      }
    }

    // Use the factory method instead of manually creating the card
    return StatsCard.deliveryStats(
      totalCalves: totalCalves,
      maleCalves: maleCalves,
      femaleCalves: femaleCalves,
      totalDeliveries: totalDeliveries,
      successfulDeliveries: successfulDeliveries,
      onInfoTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Shows detailed information about calf statistics'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      onCardTap: () {
        // Optional: Navigate to detailed delivery records view
      },
    );
  }

  Widget _buildStatusCard() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getDeliveryStatusData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        final data = snapshot.data ?? {};
        return _buildStatusCardWithData(data);
      },
    );
  }

  Future<Map<String, dynamic>> _getDeliveryStatusData() async {
    // Check eligibility criteria (same as delivery records screen)
    final isFemale = widget.cattle.gender?.toLowerCase() == 'female';
    final isPregnant = widget.cattle.breedingStatus?.isPregnant ?? false;

    // Get the expected calving date from the breeding status
    final dueDate = widget.cattle.breedingStatus?.expectedCalvingDate;

    // Get animal type settings for accurate empty period
    final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
    final animalType = animalTypes.firstWhereOrNull(
      (type) => type.businessId == widget.cattle.animalTypeId,
    );
    final emptyPeriodDays = animalType?.defaultEmptyPeriodDays ??
        (widget.cattle.animalTypeId?.toLowerCase().contains('buffalo') == true ? 90 : 60);

    // Log the status for debugging
    debugPrint('Delivery View: isFemale=$isFemale, isPregnant=$isPregnant, dueDate=$dueDate');
    debugPrint(
        'Cattle breeding status: ${widget.cattle.breedingStatus?.status}, isPregnant=${widget.cattle.breedingStatus?.isPregnant}');

    return {
      'isFemale': isFemale,
      'isPregnant': isPregnant,
      'dueDate': dueDate,
      'emptyPeriodDays': emptyPeriodDays,
    };
  }

  Widget _buildStatusCardWithData(Map<String, dynamic> data) {
    final isFemale = data['isFemale'] as bool;
    final dueDate = data['dueDate'] as DateTime?;
    final emptyPeriodDays = data['emptyPeriodDays'] as int;

    // Check if we have any active pregnancy records
    final hasActivePregnancy = _pregnancyRecords.any((record) =>
        record.status?.toLowerCase() == 'active' ||
        record.status?.toLowerCase() == 'confirmed');

    // Check for recent deliveries (completed pregnancies or delivery records)
    final hasRecentDelivery = _deliveryRecords.isNotEmpty ||
        _pregnancyRecords.any((record) => record.status?.toLowerCase() == 'completed');

    // Get the most recent delivery date
    DateTime? recentDeliveryDate;
    if (_deliveryRecords.isNotEmpty) {
      // Sort delivery records by date and get the most recent
      final sortedDeliveries = List<Map<String, dynamic>>.from(_deliveryRecords);
      sortedDeliveries.sort((a, b) {
        final dateStrA = a['deliveryDate']?.toString() ?? a['date']?.toString() ?? DateTime.now().toIso8601String();
        final dateStrB = b['deliveryDate']?.toString() ?? b['date']?.toString() ?? DateTime.now().toIso8601String();
        final dateA = DateTime.parse(dateStrA);
        final dateB = DateTime.parse(dateStrB);
        return dateB.compareTo(dateA);
      });
      final recentDelivery = sortedDeliveries.first;
      final dateStr = recentDelivery['deliveryDate']?.toString() ?? recentDelivery['date']?.toString();
      if (dateStr != null) {
        recentDeliveryDate = DateTime.parse(dateStr);
      }
    } else if (_pregnancyRecords.any((record) => record.status?.toLowerCase() == 'completed')) {
      // Use completed pregnancy record's actual calving date
      final completedRecord = _pregnancyRecords.firstWhere(
        (record) => record.status?.toLowerCase() == 'completed',
        orElse: () => PregnancyRecordIsar(),
      );
      recentDeliveryDate = completedRecord.actualCalvingDate ?? completedRecord.endDate;
    }

    // Get the expected date from the most recent active pregnancy record if available
    DateTime? expectedDate;
    if (hasActivePregnancy) {
      final activeRecord = _pregnancyRecords.firstWhere(
          (record) =>
              record.status?.toLowerCase() == 'active' ||
              record.status?.toLowerCase() == 'confirmed',
          orElse: () => PregnancyRecordIsar());
      expectedDate = activeRecord.expectedCalvingDate ?? dueDate;
      debugPrint(
          'Found active pregnancy record with expected date: $expectedDate');
    } else {
      expectedDate = dueDate;
      debugPrint('No active pregnancy records found, using breeding status date: $expectedDate');
    }

    // Determine what to show based on status
    String? customStatusText;
    String? customGuidance;

    if (!isFemale) {
      customStatusText = 'Not Available';
      customGuidance = 'Only female cattle can have delivery records.';
    } else if (hasActivePregnancy) {
      // Currently pregnant - show pregnancy info
      customStatusText = null; // Use default pregnancy status
      customGuidance = null;
    } else if (hasRecentDelivery && recentDeliveryDate != null) {
      // Recently delivered - show delivery info with next breeding eligibility
      customStatusText = 'Recent Delivery';
      final daysSinceDelivery = DateTime.now().difference(recentDeliveryDate).inDays;

      // Calculate next breeding eligibility date using animal type settings
      final nextBreedingDate = recentDeliveryDate.add(Duration(days: emptyPeriodDays));
      final daysUntilEligible = nextBreedingDate.difference(DateTime.now()).inDays;

      if (daysUntilEligible <= 0) {
        customGuidance = 'Delivered ${daysSinceDelivery == 0 ? 'today' : '$daysSinceDelivery days ago'} on ${recentDeliveryDate.day}/${recentDeliveryDate.month}/${recentDeliveryDate.year}. Ready for breeding.';
      } else {
        customGuidance = 'Delivered ${daysSinceDelivery == 0 ? 'today' : '$daysSinceDelivery days ago'} on ${recentDeliveryDate.day}/${recentDeliveryDate.month}/${recentDeliveryDate.year}. Next breeding in $daysUntilEligible days.';
      }
    } else {
      // No pregnancy and no recent delivery
      customStatusText = 'Not Available';
      customGuidance = 'Cattle is not pregnant. No delivery information available.';
    }

    return StatusCard.delivery(
      isPregnant: isFemale && hasActivePregnancy, // Use pregnancy records instead of breeding status
      dueDate: hasActivePregnancy ? expectedDate : recentDeliveryDate, // Show delivery date if not pregnant
      startDate: widget.cattle.breedingStatus?.breedingDate,
      baseColor: const Color(0xFF2E7D32), // Deep Green
      customStatusText: customStatusText,
      customGuidance: customGuidance,
      trailing: IconButton(
        icon: const Icon(Icons.info_outline, color: Color(0xFF2E7D32)),
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Delivery Status Information'),
              content: const Text(
                  'This card shows the current delivery status and expected calving date for this cattle.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        },
      ),
      actionButtons: null,
    );
  }




  Widget _buildHistoryCard() {
    // Use a stable key string
    final stableKeyString =
        'delivery_history_${widget.cattle.id}_${DateTime.now().millisecondsSinceEpoch}';

    return FutureBuilder<List<Map<String, dynamic>>>(
      key: ValueKey(stableKeyString),
      future: _databaseHelper.breedingHandler
          .getDeliveryRecordsForCattle(widget.cattle.tagId ?? ''),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Card(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error loading delivery records: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          );
        }

        final deliveryRecords = snapshot.data ?? [];

        // Sort by date, most recent first
        deliveryRecords.sort((a, b) {
          // Handle both 'deliveryDate' and 'date' field names
          final dateStrA = a['deliveryDate']?.toString() ?? a['date']?.toString() ?? DateTime.now().toIso8601String();
          final dateStrB = b['deliveryDate']?.toString() ?? b['date']?.toString() ?? DateTime.now().toIso8601String();
          final dateA = DateTime.parse(dateStrA);
          final dateB = DateTime.parse(dateStrB);
          return dateB.compareTo(dateA);
        });

        // Add cattleName and cattleId to each record for display
        for (final record in deliveryRecords) {
          record['cattleName'] = widget.cattle.name;
          record['cattleId'] = widget.cattle.tagId;
        }

        return DeliveryHistoryCard(
          records: deliveryRecords,
          title: 'Delivery History',
          emptyMessage: 'No delivery records found',
          onEdit: _editDeliveryRecord,
          onDelete: _deleteDeliveryRecord,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDeliveryRecords,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildStatsCard(),
                    const SizedBox(height: 16),
                    _buildStatusCard(),
                    const SizedBox(height: 16),
                    _buildHistoryCard(),
                    // Add bottom padding for better spacing with FAB
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            ),
      floatingActionButton: _shouldShowDeliveryFAB(),
    );
  }

  // Helper method to determine if the FAB should be shown
  Widget? _shouldShowDeliveryFAB() {

    // Check eligibility criteria (same as delivery records screen)
    // Must be female and pregnant
    final isFemale = widget.cattle.gender?.toLowerCase() == 'female';
    final isPregnant = widget.cattle.breedingStatus?.isPregnant == true;
    final hasActivePregnancy = _pregnancyRecords.any((record) =>
        record.status?.toLowerCase() == 'active' ||
        record.status?.toLowerCase() == 'confirmed');

    if (!isFemale || (!isPregnant && !hasActivePregnancy)) {
      return null;
    }

    // Get expected calving date - prioritize pregnancy record over breeding status
    DateTime? expectedDate;

    if (hasActivePregnancy) {
      final activeRecord = _pregnancyRecords.firstWhere(
          (record) =>
              record.status?.toLowerCase() == 'active' ||
              record.status?.toLowerCase() == 'confirmed',
          orElse: () => PregnancyRecordIsar());
      expectedDate = activeRecord.expectedCalvingDate;
    }

    // Fallback to breeding status if no pregnancy record date
    expectedDate ??= widget.cattle.breedingStatus?.expectedCalvingDate;

    // If no expected calving date, don't show FAB
    if (expectedDate == null) {
      return null;
    }

    final daysUntilDue = expectedDate.difference(DateTime.now()).inDays;

    // Option 1: Hide FAB completely if more than 30 days away
    // if (daysUntilDue > 30) {
    //   return null;
    // }

    // Option 2: Show FAB but with different behavior based on days remaining
    return FloatingActionButton(
      onPressed: () {
        if (daysUntilDue > 30) {
          // Show warning dialog when trying to record delivery too early
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              icon: const Icon(Icons.warning_amber_rounded,
                  color: Color(0xFFE53935), size: 40),
              title: const Text(
                'WARNING: EARLY DELIVERY',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFE53935),
                ),
                textAlign: TextAlign.center,
              ),
              content: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                  ),
                  children: [
                    const TextSpan(
                      text: 'The expected calving date is still ',
                    ),
                    TextSpan(
                      text: '$daysUntilDue days away',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFFE53935),
                      ),
                    ),
                    const TextSpan(
                      text:
                          '.\n\nAre you sure you want to record a delivery at this time?',
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('CANCEL',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showDeliveryForm(context);
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFFE53935),
                  ),
                  child: const Text('PROCEED ANYWAY',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
          );
        } else {
          // Within 30 days, proceed directly to form
          _showDeliveryForm(context);
        }
      },
      backgroundColor: _getDueDateColor(),
      tooltip: 'Record Delivery',
      child: const Icon(Icons.add),
    );
  }

  // Helper method to determine FAB color based on due date
  Color _getDueDateColor() {
    // Use the same color (Deep Green) for all states
    return AppColors.primary;
  }

  // Helper method to convert numberOfCalves string to integer
  int _convertNumberOfCalvesToInt(String numberOfCalves) {
    switch (numberOfCalves.toLowerCase()) {
      case 'single':
        return 1;
      case 'twins':
        return 2;
      case 'triplets':
        return 3;
      case 'quadruplets':
        return 4;
      default:
        // Try to parse as integer, fallback to 1
        return int.tryParse(numberOfCalves) ?? 1;
    }
  }

  void _showDeliveryForm(BuildContext context) async {
    if (!mounted) return;
    final initialContext = context;

    try {
      setState(() => _isLoading = true);

      // We don't need to reassign _allCattle here as it's already loaded in _loadDeliveryRecords

      if (!mounted) return;
      setState(() => _isLoading = false);

      if (!mounted || !initialContext.mounted) return;
      final result = await showDialog<Map<String, dynamic>>(
        context: initialContext,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) => DeliveryFormDialog(
          motherTagId: widget.cattle.tagId ?? '',
          existingCattle: _allCattle,
        ),
      );

      if (result != null && mounted) {
        // Process the delivery result
        try {
          // Find the active pregnancy record to link the delivery
          PregnancyRecordIsar? activePregnancyRecord;
          try {
            activePregnancyRecord = _pregnancyRecords.firstWhere(
              (record) =>
                  record.status?.toLowerCase() == 'confirmed' ||
                  record.status?.toLowerCase() == 'active',
            );
          } catch (e) {
            // No active pregnancy record found
            activePregnancyRecord = null;
          }

          // Create delivery record in database
          final numberOfCalvesInt = _convertNumberOfCalvesToInt(result['numberOfCalves']);
          final deliveryRecord = {
            'motherTagId': result['motherTagId'],
            'deliveryDate': result['deliveryDate'],  // Fixed: use 'deliveryDate' not 'date'
            'deliveryType': result['deliveryType'],  // Fixed: use 'deliveryType' not 'type'
            'numberOfCalves': numberOfCalvesInt,
            'notes': result['notes'],
            'pregnancyId': activePregnancyRecord?.businessId, // Link to pregnancy record for cascade deletion
            'calfDetails': result['calves']?.map((calf) => {
              'tagId': calf.tagId,
              'name': calf.name,
              'gender': calf.gender,
            }).toList(),
          };

          debugPrint('Creating delivery record: $deliveryRecord');
          try {
            await _databaseHelper.breedingHandler.addDeliveryRecordFromMap(deliveryRecord);
            debugPrint('✅ Delivery record created successfully');
          } catch (e) {
            debugPrint('❌ Error creating delivery record: $e');
            rethrow;
          }

          // Save new calves to database
          if (result['calves'] != null) {
            debugPrint('Processing ${result['calves'].length} calves from delivery result');
            for (var calf in result['calves']) {
              debugPrint('Adding calf: ${calf.name} (${calf.tagId})');
              final updatedCalf = CattleIsar.fromMap(calf.toMap());
              await _databaseHelper.cattleHandler.addCattle(updatedCalf);

              // Notify about new cattle being added
              final streamService = GetIt.instance<StreamService>();
              streamService.notifyCattleChange({
                'action': 'add',
                'cattleId': updatedCalf.tagId,
                'data': updatedCalf.toMap(),
              });
              debugPrint('✅ Notified cattle stream about new calf: ${calf.name} (${calf.tagId})');
            }
          } else {
            debugPrint('No calves found in delivery result');
          }

          // Update the active pregnancy record status to Completed (already found above)

          if (activePregnancyRecord != null) {
            debugPrint('Updating pregnancy record ${activePregnancyRecord.businessId} to Completed');
            final updatedPregnancyRecord = activePregnancyRecord.copyWith(
              status: 'Completed',
              actualCalvingDate: DateTime.now(),
              endDate: DateTime.now(),
            );

            await _databaseHelper.breedingHandler
                .updatePregnancyRecord(updatedPregnancyRecord, updateLinkedBreedingRecord: true);

            // Notify listeners of the pregnancy update
            _databaseHelper.notifyRecordUpdate(
                'pregnancy', 'update', widget.cattle.tagId ?? '', updatedPregnancyRecord.toMap());
          }

          // Update mother's breeding status to not pregnant
          BreedingStatus updatedBreedingStatus;
          final deliveryDate = DateTime.now();
          debugPrint('🔄 Setting lastCalvingDate to: $deliveryDate');

          if (widget.cattle.breedingStatus != null) {
            updatedBreedingStatus = BreedingStatus();
            updatedBreedingStatus.status = 'Open'; // Set to Open after delivery
            updatedBreedingStatus.lastHeatDate =
                widget.cattle.breedingStatus!.lastHeatDate;
            updatedBreedingStatus.isPregnant = false;
            updatedBreedingStatus.breedingDate =
                widget.cattle.breedingStatus!.breedingDate;
            updatedBreedingStatus.expectedCalvingDate = null;
            updatedBreedingStatus.nextHeatDate =
                widget.cattle.breedingStatus!.nextHeatDate;
            updatedBreedingStatus.lastBreedingMethod =
                widget.cattle.breedingStatus!.lastBreedingMethod;
            updatedBreedingStatus.lastCalvingDate = deliveryDate; // Set to today
          } else {
            updatedBreedingStatus = BreedingStatus();
            updatedBreedingStatus.isPregnant = false;
            updatedBreedingStatus.status = 'Open';
            updatedBreedingStatus.lastCalvingDate = deliveryDate;
          }

          // Update the main cattle status to lactating after delivery
          final updatedCattle = widget.cattle.copyWith(
            breedingStatus: updatedBreedingStatus,
            status: 'lactating', // Set main status to lactating for milk recording
          );

          debugPrint('🔄 Updated breeding status lastCalvingDate: ${updatedBreedingStatus.lastCalvingDate}');
          debugPrint('🔄 About to save cattle with lastCalvingDate: ${updatedCattle.breedingStatus?.lastCalvingDate} and status: ${updatedCattle.status}');
          await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

          // Notify parent of the cattle update FIRST
          debugPrint('🔄 About to notify parent with cattle lastCalvingDate: ${updatedCattle.breedingStatus?.lastCalvingDate} and status: ${updatedCattle.status}');
          widget.onCattleUpdated(updatedCattle);
          debugPrint('🔄 Successfully notified parent with cattle lastCalvingDate: ${updatedCattle.breedingStatus?.lastCalvingDate} and status: ${updatedCattle.status}');

          // Refresh delivery records to show the new delivery
          await _loadDeliveryRecords();

          // Wait a moment to ensure database operations are committed
          await Future.delayed(const Duration(milliseconds: 200));

          // Force a complete UI refresh by getting fresh cattle data
          final freshCattle = await _databaseHelper.cattleHandler
              .getCattleByTagId(widget.cattle.tagId ?? '');
          if (freshCattle != null) {
            debugPrint('🔄 Fresh cattle data lastCalvingDate: ${freshCattle.breedingStatus?.lastCalvingDate} and status: ${freshCattle.status}');
            widget.onCattleUpdated(freshCattle);
          }

          if (!mounted || !initialContext.mounted) return;
          final message = BreedingMessageUtils.deliveryRecordCreated();
          BreedingMessageUtils.showSuccess(initialContext, message);

          // Force a complete rebuild
          setState(() {});
        } catch (e) {
          if (!mounted || !initialContext.mounted) return;
          BreedingMessageUtils.showError(initialContext, 'Error recording delivery: $e');
        }
      }
    } catch (e) {
      if (!mounted || !initialContext.mounted) return;
      setState(() => _isLoading = false);
      BreedingMessageUtils.showError(initialContext, 'Error: $e');
    }
  }

  // Method to edit a delivery record
  Future<void> _editDeliveryRecord(Map<String, dynamic> record) async {
    if (!mounted) return;

    try {
      setState(() => _isLoading = true);

      // Show the delivery form dialog with the current record data
      if (!mounted) return;
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (dialogContext) => DeliveryFormDialog(
          motherTagId: widget.cattle.tagId ?? '',
          existingCattle: _allCattle,
          record: record,
        ),
      );

      // If the user submitted the form
      if (result != null && mounted) {
        // Preserve the record ID
        final updatedRecord = Map<String, dynamic>.from(result);
        updatedRecord['id'] = record['id'];

        // Update the delivery record
        await _databaseHelper.breedingHandler
            .updateDeliveryRecordFromMap(updatedRecord);

        // Refresh the data
        await _loadDeliveryRecords();

        if (!mounted) return;
        final message = BreedingMessageUtils.deliveryRecordUpdated();
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      if (!mounted) return;
      BreedingMessageUtils.showError(context, 'Error updating delivery record: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Method to delete a delivery record
  Future<void> _deleteDeliveryRecord(Map<String, dynamic> record) async {
    if (!mounted) return;

    // Find linked records that will be cascade deleted
    List<String> cascadeWarnings = [];

    try {
      // Get the delivery record to find linked pregnancy
      final deliveryRecord = await _databaseHelper.breedingHandler
          .getDeliveryRecordsForCattle(widget.cattle.tagId ?? '');

      final currentDelivery = deliveryRecord.firstWhere(
        (dr) => dr['id'] == record['id'],
        orElse: () => <String, dynamic>{},
      );

      if (currentDelivery.isNotEmpty && currentDelivery['pregnancyId'] != null) {
        final pregnancyId = currentDelivery['pregnancyId'];
        cascadeWarnings.add('1 pregnancy record');
        cascadeWarnings.add(pregnancyId);

        // Get the pregnancy record to find linked breeding record
        final pregnancyRecords = await _databaseHelper.breedingHandler
            .getPregnancyRecordsForCattle(widget.cattle.tagId ?? '');

        final linkedPregnancy = pregnancyRecords.firstWhere(
          (pr) => pr.businessId == pregnancyId,
          orElse: () => PregnancyRecordIsar(),
        );

        if (linkedPregnancy.breedingRecordId != null) {
          cascadeWarnings.add('1 breeding record');
          cascadeWarnings.add(linkedPregnancy.breedingRecordId!);
        }
      }
    } catch (e) {
      debugPrint('Error finding linked records: $e');
      // Continue with deletion even if we can't find linked records
    }

    // Show standardized confirmation dialog with cascade warnings
    final cattleDisplayName = widget.cattle.name != null && widget.cattle.tagId != null
        ? '${widget.cattle.name} (${widget.cattle.tagId})'
        : widget.cattle.name ?? widget.cattle.tagId ?? 'Unknown';
    if (!mounted) return;

    final confirmed = await BreedingMessageUtils.showDeliveryDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: record['id']?.toString(),
      specificRecords: cascadeWarnings,
    );

    if (confirmed == true) {
      try {
        setState(() => _isLoading = true);

        // Delete the delivery record
        debugPrint('🗑️ Deleting delivery record: ${record['id']}');
        await _databaseHelper.breedingHandler
            .deleteDeliveryRecord(record['id']);

        // Refresh the data
        await _loadDeliveryRecords();

        // Force a complete UI refresh by getting fresh cattle data after deletion
        final freshCattle = await _databaseHelper.cattleHandler
            .getCattleByTagId(widget.cattle.tagId ?? '');
        if (freshCattle != null) {
          debugPrint('🗑️ Fresh cattle after deletion - lastCalvingDate: ${freshCattle.breedingStatus?.lastCalvingDate} and status: ${freshCattle.status}');
          widget.onCattleUpdated(freshCattle);
        }

        if (!mounted) return;
        final message = BreedingMessageUtils.deliveryRecordDeleted();
        BreedingMessageUtils.showSuccess(context, message);
      } catch (e) {
        if (!mounted) return;
        BreedingMessageUtils.showError(context, 'Error deleting delivery record: $e');
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }
}
