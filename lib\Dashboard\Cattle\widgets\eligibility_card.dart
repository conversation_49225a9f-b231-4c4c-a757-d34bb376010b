import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';
import 'card_header.dart';

// Add a class to hold eligibility check results
class EligibilityCheckResult {
  final bool isEligible;
  final String statusMessage;
  final String reasonMessage;
  final Color statusColor;
  final IconData statusIcon;
  final String? nextEligibleDateMessage;

  const EligibilityCheckResult({
    required this.isEligible,
    required this.statusMessage,
    required this.reasonMessage,
    required this.statusColor,
    required this.statusIcon,
    this.nextEligibleDateMessage,
  });
}

/// A reusable card widget that displays eligibility status for various operations.
///
/// This can be used for breeding eligibility, pregnancy eligibility, etc.
class EligibilityCard extends StatelessWidget {
  // Animal data properties
  final String gender;
  final DateTime? dateOfBirth;
  final DateTime? purchaseDate;
  final String cattleId;
  final String animalTypeId;

  // Status properties
  final bool isPregnant;
  final DateTime? lastBreedingDate;
  final DateTime? lastCalvingDate;
  final List<Map<String, dynamic>>? breedingRecords;
  final int? animalTypeEmptyPeriodDays;

  // Customization properties
  final int minimumAgeMonths;
  final String title;
  final Color eligibleColor;
  final Color ineligibleColor;
  final Color warningColor;
  final IconData eligibleIcon;
  final IconData ineligibleIcon;
  final VoidCallback? onAddPressed;
  final Widget? trailing;
  final List<Widget>? additionalContent;

  // Advanced eligibility properties
  final String? eligibilityReason;
  final String? additionalInfo;
  final DateTime? nextEligibleDate;
  final bool forceEligible;

  // Status message properties (used by factory methods)
  final String? overrideStatusMessage;
  final String? overrideReasonMessage;
  final Color? overrideStatusColor;
  final IconData? overrideStatusIcon;
  final bool ignoreBasicEligibilityChecks;
  final String? nextEligibleDateMessage;

  const EligibilityCard({
    super.key,
    required this.gender,
    required this.dateOfBirth,
    this.purchaseDate,
    required this.cattleId,
    this.animalTypeId = '',
    this.isPregnant = false,
    this.lastBreedingDate,
    this.lastCalvingDate,
    this.breedingRecords,
    this.animalTypeEmptyPeriodDays,
    this.minimumAgeMonths = 15,
    this.title = 'Breeding Eligibility',
    this.eligibleColor = const Color(0xFF2E7D32),
    this.ineligibleColor = const Color(0xFF795548),
    this.warningColor = const Color(0xFF2196F3),
    this.eligibleIcon = Icons.check_circle,
    this.ineligibleIcon = Icons.cancel,
    this.onAddPressed,
    this.trailing,
    this.additionalContent,
    this.eligibilityReason,
    this.additionalInfo,
    this.nextEligibleDate,
    this.forceEligible = false,
    this.overrideStatusMessage,
    this.overrideReasonMessage,
    this.overrideStatusColor,
    this.overrideStatusIcon,
    this.ignoreBasicEligibilityChecks = false,
    this.nextEligibleDateMessage,
  });

  /// Factory for breeding eligibility with complex rules
  factory EligibilityCard.breeding({
    required String gender,
    required String cattleId,
    required String animalTypeId,
    required bool isPregnant,
    required DateTime? dateOfBirth,
    DateTime? purchaseDate,
    DateTime? lastBreedingDate,
    DateTime? lastCalvingDate,
    List<Map<String, dynamic>>? breedingRecords,
    int? animalTypeEmptyPeriodDays,
    VoidCallback? onAddPressed,
    Widget? trailing,
    List<Widget>? additionalContent,
  }) {
    // Get current date

    // Check if male (only females can be bred)
    if (gender.toLowerCase() == 'male') {
      return EligibilityCard(
        gender: gender,
        dateOfBirth: dateOfBirth,
        purchaseDate: purchaseDate,
        cattleId: cattleId,
        animalTypeId: animalTypeId,
        minimumAgeMonths: 24, // Not relevant for males
        overrideStatusMessage: 'Not Eligible for Breeding',
        overrideReasonMessage: 'Only female cattle can be bred',
        overrideStatusColor: const Color(0xFFF44336), // Red
        overrideStatusIcon: Icons.block,
        ignoreBasicEligibilityChecks: true,
        onAddPressed: null, // Disable add button for males
        trailing: trailing,
      );
    }

    // Check if currently pregnant
    if (isPregnant) {
      return EligibilityCard(
        gender: gender,
        dateOfBirth: dateOfBirth,
        purchaseDate: purchaseDate,
        cattleId: cattleId,
        animalTypeId: animalTypeId,
        isPregnant: isPregnant,
        minimumAgeMonths: 24,
        overrideStatusMessage: 'Not Eligible for Breeding',
        overrideReasonMessage: 'Cattle is currently pregnant',
        overrideStatusColor: const Color(0xFFF44336), // Red
        overrideStatusIcon: Icons.pregnant_woman,
        ignoreBasicEligibilityChecks: true,
        onAddPressed: null, // Disable add button if pregnant
        trailing: trailing,
      );
    }

    // Check for pending breeding records
    if (breedingRecords != null && breedingRecords.isNotEmpty) {
      final sortedRecords = List<Map<String, dynamic>>.from(breedingRecords);

      // Sort by date, most recent first
      sortedRecords.sort((a, b) {
        final dateA =
            DateTime.parse(a['date'] ?? DateTime.now().toIso8601String());
        final dateB =
            DateTime.parse(b['date'] ?? DateTime.now().toIso8601String());
        return dateB.compareTo(dateA);
      });

      final latestRecord = sortedRecords.first;
      final latestStatus =
          latestRecord['status']?.toString().toLowerCase() ?? '';

      // Check if there's a confirmed breeding record (indicating pregnancy)
      // Only block breeding if the cattle is actually pregnant (not just has completed breeding)
      if (latestStatus == 'confirmed' && isPregnant) {
        return EligibilityCard(
          gender: gender,
          dateOfBirth: dateOfBirth,
          purchaseDate: purchaseDate,
          cattleId: cattleId,
          animalTypeId: animalTypeId,
          isPregnant: true, // Override isPregnant to true
          minimumAgeMonths: 24,
          overrideStatusMessage: 'Not Eligible for Breeding',
          overrideReasonMessage: 'Cattle has an active pregnancy record',
          overrideStatusColor: const Color(0xFFF44336), // Red
          overrideStatusIcon: Icons.pregnant_woman,
          ignoreBasicEligibilityChecks: true,
          onAddPressed: null, // Disable add button if pregnant
          trailing: trailing,
        );
      }

      // Check if there's a pending breeding record
      if (latestStatus == 'pending') {
        final latestBreedingDate = DateTime.parse(
            latestRecord['date'] ?? DateTime.now().toIso8601String());
        final formattedDate =
            DateTime.now().difference(latestBreedingDate).inDays;
        final nextHeatDate = latestBreedingDate.add(const Duration(days: 21));
        final daysUntilNextHeat =
            nextHeatDate.difference(DateTime.now()).inDays;
        final nextEligibleMsg = daysUntilNextHeat > 0
            ? 'Next heat expected in $daysUntilNextHeat days (${DateFormat('MMMM dd, yyyy').format(nextHeatDate)})'
            : 'Heat cycle may have passed. Check pregnancy status.';

        return EligibilityCard(
          gender: gender,
          dateOfBirth: dateOfBirth,
          purchaseDate: purchaseDate,
          cattleId: cattleId,
          animalTypeId: animalTypeId,
          breedingRecords: breedingRecords,
          minimumAgeMonths: 24,
          overrideStatusMessage: 'Pending Breeding Record',
          overrideReasonMessage:
              'Breeding attempt on ${DateFormat('MMMM dd, yyyy').format(latestBreedingDate)} ($formattedDate days ago) is still pending confirmation',
          overrideStatusColor: const Color(0xFF2196F3), // Blue
          overrideStatusIcon: Icons.pending,
          ignoreBasicEligibilityChecks: true,
          onAddPressed: null, // Disable add button if there's a pending record
          trailing: trailing,
          nextEligibleDateMessage: nextEligibleMsg,
        );
      }

      // Check if there was a recent breeding failure (within 21 days)
      if (latestStatus == 'failed') {
        final latestBreedingDate = DateTime.parse(
            latestRecord['date'] ?? DateTime.now().toIso8601String());
        final daysSinceLastBreeding =
            DateTime.now().difference(latestBreedingDate).inDays;

        if (daysSinceLastBreeding < 21) {
          // Calculate when the cattle will be eligible again
          final nextEligibleDate =
              latestBreedingDate.add(const Duration(days: 21));

          return EligibilityCard(
            gender: gender,
            dateOfBirth: dateOfBirth,
            purchaseDate: purchaseDate,
            cattleId: cattleId,
            animalTypeId: animalTypeId,
            breedingRecords: breedingRecords,
            minimumAgeMonths: 24,
            overrideStatusMessage: 'Not Recommended',
            overrideReasonMessage:
                'Recent breeding failure (${daysSinceLastBreeding.toString()} days ago)',
            overrideStatusColor: const Color(0xFF2196F3), // Blue
            overrideStatusIcon: Icons.warning,
            ignoreBasicEligibilityChecks: true,
            onAddPressed: onAddPressed, // Allow but not recommended
            trailing: trailing,
            nextEligibleDateMessage:
                'Recommended to wait until ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}',
          );
        }
      }
    }

    // If we reached here, use standard eligibility checks with the minimum age for breeding
    return EligibilityCard(
      gender: gender,
      dateOfBirth: dateOfBirth,
      purchaseDate: purchaseDate,
      cattleId: cattleId,
      animalTypeId: animalTypeId,
      breedingRecords: breedingRecords,
      lastBreedingDate: lastBreedingDate,
      lastCalvingDate: lastCalvingDate,
      isPregnant: isPregnant,
      minimumAgeMonths:
          24, // Default minimum age if not specified in animal type
      animalTypeEmptyPeriodDays: animalTypeEmptyPeriodDays,
      onAddPressed: onAddPressed,
      trailing: trailing,
      additionalContent: additionalContent,
    );
  }

  /// Factory constructor for pregnancy eligibility card
  factory EligibilityCard.pregnancy({
    required String gender,
    required DateTime? dateOfBirth,
    DateTime? purchaseDate,
    required String cattleId,
    String? animalTypeId,
    bool? isPregnant,
    DateTime? lastCalvingDate,
    int? animalTypeEmptyPeriodDays,
    List<Map<String, dynamic>>? breedingRecords,
    VoidCallback? onAddPressed,
    Widget? trailing,
  }) {
    // Check for pending breeding records
    if (breedingRecords != null && breedingRecords.isNotEmpty) {
      // Sort by date, most recent first
      final sortedRecords = List<Map<String, dynamic>>.from(breedingRecords);

      sortedRecords.sort((a, b) {
        final dateA = DateTime.parse(a['date'] ?? DateTime.now().toIso8601String());
        final dateB = DateTime.parse(b['date'] ?? DateTime.now().toIso8601String());
        return dateB.compareTo(dateA);
      });

      // Check if there's a pending breeding record
      final hasPendingBreeding = sortedRecords.any(
          (record) => record['status']?.toString().toLowerCase() == 'pending');

      if (hasPendingBreeding) {
        // Find the pending breeding record to get its date
        final pendingRecord = sortedRecords.firstWhere(
            (record) => record['status']?.toString().toLowerCase() == 'pending');
        final breedingDate = DateTime.parse(pendingRecord['date'] ?? DateTime.now().toIso8601String());
        final formattedDate = DateFormat('MMMM dd, yyyy').format(breedingDate);

        return EligibilityCard(
          gender: gender,
          dateOfBirth: dateOfBirth,
          purchaseDate: purchaseDate,
          cattleId: cattleId,
          animalTypeId: animalTypeId ?? '',
          minimumAgeMonths: 18,
          overrideStatusMessage: 'Pending Breeding Record',
          overrideReasonMessage: 'Breeding attempt on $formattedDate is still pending confirmation',
          overrideStatusColor: const Color(0xFF2196F3), // Blue
          overrideStatusIcon: Icons.pending,
          ignoreBasicEligibilityChecks: true,
          onAddPressed: null, // Disable add button if there's a pending record
          trailing: trailing,
          nextEligibleDateMessage: 'Please update the pending breeding record before adding a pregnancy record',
        );
      }
    }

    return EligibilityCard(
      gender: gender,
      dateOfBirth: dateOfBirth,
      purchaseDate: purchaseDate,
      cattleId: cattleId,
      animalTypeId: animalTypeId ?? '', // Default, not needed for pregnancy checks
      isPregnant: isPregnant ?? false,
      lastCalvingDate: lastCalvingDate,
      animalTypeEmptyPeriodDays: animalTypeEmptyPeriodDays,
      minimumAgeMonths: 18, // Pregnancy checks can start earlier
      onAddPressed: onAddPressed,
      trailing: trailing,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get eligibility status
    final eligibilityStatus = _checkEligibility();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CardHeader(
            title: eligibilityStatus.statusText,
            color: eligibilityStatus.color,
            icon: eligibilityStatus.icon,
            trailing: trailing,
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Age information - only show if no breeding records or explicitly requested
                if (breedingRecords == null || breedingRecords!.isEmpty) ...[
                  InfoRow(
                    icon: Icons.cake,
                    label: _getAgeLabel(),
                    value: _getAgeValue(),
                    color: const Color(0xFF1976D2),
                  ),
                  const SizedBox(height: 12),
                ],

                // Status
                InfoRow(
                  icon: Icons.check_circle,
                  label: 'Status',
                  value: eligibilityStatus.reasonText,
                  color: eligibilityStatus.color,
                  isHighlighted: true,
                ),

                // Show next eligible date message if available
                if (eligibilityStatus.nextEligibleDateMessage != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: eligibilityStatus.color
                          .withAlpha(26), // 0.1 * 255 = 26
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: eligibilityStatus.color
                            .withAlpha(51), // 0.2 * 255 = 51
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.event_available,
                          color: eligibilityStatus.color,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            eligibilityStatus.nextEligibleDateMessage!,
                            style: TextStyle(
                              color: eligibilityStatus.color,
                              fontWeight: FontWeight.w500,
                            ),
                            softWrap: true,
                            overflow: TextOverflow.visible,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                if (additionalContent != null) ...[
                  const SizedBox(height: 16),
                  ...additionalContent!,
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getAgeLabel() {
    if (dateOfBirth != null) return 'Age';
    if (purchaseDate != null) return 'Estimated Age';
    return 'Unknown Age';
  }

  String _getAgeValue() {
    if (dateOfBirth != null) {
      final now = DateTime.now();
      final ageInMonths = ((now.year - dateOfBirth!.year) * 12) +
          (now.month - dateOfBirth!.month);
      return '$ageInMonths months';
    } else if (purchaseDate != null) {
      final now = DateTime.now();
      const estimatedMonthsAtPurchase = 24; // Assume 2 years at purchase
      final monthsSincePurchase = ((now.year - purchaseDate!.year) * 12) +
          (now.month - purchaseDate!.month);
      final calculatedAge = monthsSincePurchase + estimatedMonthsAtPurchase;
      return '$calculatedAge months (estimated)';
    }
    return 'Age information not available';
  }

  // Check eligibility based on all factors
  _EligibilityStatus _checkEligibility() {
    // If using override values, return those
    if (ignoreBasicEligibilityChecks &&
        overrideStatusMessage != null &&
        overrideReasonMessage != null) {
      return _EligibilityStatus(
        isEligible: onAddPressed != null, // Eligible if add button enabled
        statusText: overrideStatusMessage!,
        reasonText: overrideReasonMessage!,
        color: overrideStatusColor ?? ineligibleColor,
        icon: overrideStatusIcon ?? ineligibleIcon,
        nextEligibleDateMessage: nextEligibleDateMessage,
      );
    }

    // Check for pending breeding records
    if (breedingRecords != null && breedingRecords!.isNotEmpty) {
      final sortedRecords = List<Map<String, dynamic>>.from(breedingRecords!);

      // Sort by date, most recent first
      sortedRecords.sort((a, b) {
        final dateA =
            DateTime.parse(a['date'] ?? DateTime.now().toIso8601String());
        final dateB =
            DateTime.parse(b['date'] ?? DateTime.now().toIso8601String());
        return dateB.compareTo(dateA);
      });

      final latestRecord = sortedRecords.first;
      final latestStatus =
          latestRecord['status']?.toString().toLowerCase() ?? '';

      if (latestStatus == 'pending') {
        final latestBreedingDate = DateTime.parse(
            latestRecord['date'] ?? DateTime.now().toIso8601String());
        final formattedDate =
            DateFormat('MMMM dd, yyyy').format(latestBreedingDate);

        return _EligibilityStatus(
          isEligible: false,
          statusText: 'Pending Breeding Record',
          reasonText:
              'Breeding attempt on $formattedDate is still pending confirmation',
          color: const Color(0xFF2196F3), // Blue
          icon: Icons.pending,
          nextEligibleDateMessage:
              'Please update the pending breeding record before adding a new one',
        );
      }
    }

    // Override with forced eligibility
    if (forceEligible) {
      return _EligibilityStatus(
        isEligible: true,
        statusText: title,
        reasonText: eligibilityReason ?? 'All conditions are met',
        color: eligibleColor,
        icon: eligibleIcon,
        nextEligibleDateMessage: nextEligibleDate != null
            ? 'Eligible after ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate!)}'
            : null,
      );
    }

    // Check gender - only females are eligible
    if (gender.toLowerCase() != 'female') {
      return _EligibilityStatus(
        isEligible: false,
        statusText: 'Not Eligible',
        reasonText: 'Only female cattle can be processed',
        color: ineligibleColor,
        icon: ineligibleIcon,
      );
    }

    // Check age
    final now = DateTime.now();

    // Handle case when both date of birth and purchase date are missing
    if (dateOfBirth == null && purchaseDate == null) {
      return _EligibilityStatus(
        isEligible: false,
        statusText: 'Cannot Determine Eligibility',
        reasonText: 'Age information missing',
        color: warningColor,
        icon: Icons.warning,
      );
    }

    // Calculate age in months - use date of birth if available, otherwise use purchase date
    int ageInMonths;
    bool isEstimated = false;
    const int minimumBreedingAgeMonths = 24; // Standard minimum for breeding

    if (dateOfBirth != null) {
      ageInMonths = ((now.year - dateOfBirth!.year) * 12) +
          (now.month - dateOfBirth!.month);
    } else {
      // For purchased animals, use purchase date with estimated age at purchase
      const estimatedMonthsAtPurchase = 24; // Assume 2 years at purchase
      final monthsSincePurchase = ((now.year - purchaseDate!.year) * 12) +
          (now.month - purchaseDate!.month);
      ageInMonths = estimatedMonthsAtPurchase + monthsSincePurchase;
      isEstimated = true;
    }

    // Return age ineligibility if too young
    if (ageInMonths < minimumBreedingAgeMonths) {
      final monthsRemaining = minimumBreedingAgeMonths - ageInMonths;
      final eligibleDate =
          DateTime(now.year, now.month + monthsRemaining, now.day);

      return _EligibilityStatus(
        isEligible: false,
        statusText: 'Not Eligible - Too Young',
        reasonText:
            'Age: $ageInMonths months${isEstimated ? " (estimated)" : ""} (minimum: $minimumBreedingAgeMonths months)',
        color: ineligibleColor,
        icon: Icons.child_care,
        nextEligibleDateMessage:
            'Will be eligible after ${DateFormat('MMMM dd, yyyy').format(eligibleDate)}',
      );
    }

    // Check pregnancy status
    if (isPregnant) {
      return _EligibilityStatus(
        isEligible: false,
        statusText: 'Not Eligible',
        reasonText: 'Cattle is currently pregnant',
        color: ineligibleColor,
        icon: Icons.pregnant_woman,
      );
    }

    // Check post-calving empty period
    if (lastCalvingDate != null) {
      final now = DateTime.now();
      final calvingDate = lastCalvingDate!; // Safe to use ! since we checked for null
      final daysSinceCalving = now.difference(calvingDate).inDays;

      // Use animal type empty period or fallback based on animal type
      final requiredEmptyDays = animalTypeEmptyPeriodDays ??
          (animalTypeId.toLowerCase().contains('buffalo') ? 90 : 60);

      if (daysSinceCalving < requiredEmptyDays) {
        final nextEligibleDate = calvingDate.add(Duration(days: requiredEmptyDays));
        final animalType = animalTypeId.toLowerCase().contains('buffalo') ? 'Buffaloes' : 'Cattle';
        final nextEligibleMsg = 'Will be eligible for breeding after ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}';

        return _EligibilityStatus(
          isEligible: false,
          statusText: 'In Empty Period',
          reasonText: '$animalType need ${requiredEmptyDays ~/ 30} months rest after calving',
          color: const Color(0xFF2196F3), // Blue
          icon: Icons.hourglass_empty,
          nextEligibleDateMessage: nextEligibleMsg,
        );
      }
    }

    // All checks passed - eligible
    return _EligibilityStatus(
      isEligible: true,
      statusText: 'Eligible for Breeding',
      reasonText: 'All requirements are met',
      color: eligibleColor,
      icon: eligibleIcon,
    );
  }

  /// Static method to check breeding eligibility without creating a widget
  static EligibilityCheckResult checkBreedingEligibility({
    required String gender,
    required String cattleId,
    required String animalTypeId,
    required bool isPregnant,
    required DateTime? dateOfBirth,
    DateTime? purchaseDate,
    DateTime? lastBreedingDate,
    DateTime? lastCalvingDate,
    List<Map<String, dynamic>>? breedingRecords,
    int? animalTypeBreedingAge,
    int? animalTypeEmptyPeriodDays,
  }) {
    // Get current date
    final now = DateTime.now();

    // Validate required fields
    if (cattleId.isEmpty) {
      return const EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Cannot Determine Eligibility',
        reasonMessage: 'Cattle ID is missing',
        statusColor: Color(0xFFFF9800), // Orange
        statusIcon: Icons.warning,
      );
    }

    // Check if male (only females can be bred)
    if (gender.toLowerCase() == 'male') {
      return const EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Not Eligible for Breeding',
        reasonMessage: 'Only female cattle can be bred',
        statusColor: Color(0xFFF44336), // Red
        statusIcon: Icons.block,
      );
    }

    // Check if currently pregnant
    if (isPregnant) {
      return const EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Not Eligible for Breeding',
        reasonMessage: 'Cattle is currently pregnant',
        statusColor: Color(0xFFF44336), // Red
        statusIcon: Icons.pregnant_woman,
      );
    }

    // Check age eligibility
    // If no date of birth and no purchase date, can't determine age
    if (dateOfBirth == null && purchaseDate == null) {
      return const EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Cannot Determine Eligibility',
        reasonMessage: 'Age information missing',
        statusColor: Color(0xFFFF9800), // Orange
        statusIcon: Icons.warning,
      );
    }

    // Calculate age
    int ageInMonths;
    bool isEstimated = false;
    final minimumBreedingAgeMonths = animalTypeBreedingAge ?? 24; // Use animal type's breeding age or default to 24 months

    if (dateOfBirth != null) {
      ageInMonths = ((now.year - dateOfBirth.year) * 12) + (now.month - dateOfBirth.month);
    } else {
      // For purchased animals, use purchase date with estimated age at purchase
      const estimatedMonthsAtPurchase = 24; // Assume 2 years at purchase
      final monthsSincePurchase = ((now.year - purchaseDate!.year) * 12) + (now.month - purchaseDate.month);
      ageInMonths = estimatedMonthsAtPurchase + monthsSincePurchase;
      isEstimated = true;
    }

    // Return age ineligibility if too young
    if (ageInMonths < minimumBreedingAgeMonths) {
      final monthsRemaining = minimumBreedingAgeMonths - ageInMonths;
      final eligibleDate = DateTime(now.year, now.month + monthsRemaining, now.day);

      return EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Not Eligible - Too Young',
        reasonMessage: 'Age: $ageInMonths months${isEstimated ? " (estimated)" : ""} (minimum: $minimumBreedingAgeMonths months)',
        statusColor: const Color(0xFFF44336), // Red
        statusIcon: Icons.child_care,
        nextEligibleDateMessage: 'Will be eligible after ${DateFormat('MMMM dd, yyyy').format(eligibleDate)}',
      );
    }

    // Check active breeding records if provided
    if (breedingRecords != null && breedingRecords.isNotEmpty) {
      final sortedRecords = List<Map<String, dynamic>>.from(breedingRecords);

      // Sort by date, most recent first
      sortedRecords.sort((a, b) {
        final dateA = DateTime.parse(a['date'] ?? DateTime.now().toIso8601String());
        final dateB = DateTime.parse(b['date'] ?? DateTime.now().toIso8601String());
        return dateB.compareTo(dateA);
      });

      final latestRecord = sortedRecords.first;
      final latestBreedingDate = DateTime.parse(latestRecord['date'] ?? DateTime.now().toIso8601String());
      final daysSinceLastBreeding = now.difference(latestBreedingDate).inDays;
      final latestStatus = latestRecord['status']?.toString().toLowerCase() ?? '';

      // Check if there's a pending breeding record
      if (latestStatus == 'pending') {
        final nextHeatDate = latestBreedingDate.add(const Duration(days: 21));
        final daysUntilNextHeat = nextHeatDate.difference(now).inDays;
        final nextEligibleMsg = daysUntilNextHeat > 0
            ? 'Next heat expected in $daysUntilNextHeat days (${DateFormat('MMMM dd, yyyy').format(nextHeatDate)})'
            : 'Heat cycle may have passed. Check pregnancy status.';

        return EligibilityCheckResult(
          isEligible: false,
          statusMessage: 'Pending Breeding Record',
          reasonMessage: 'Breeding attempt on ${DateFormat('MMMM dd, yyyy').format(latestBreedingDate)} is still pending confirmation',
          statusColor: const Color(0xFF2196F3), // Blue
          statusIcon: Icons.pending,
          nextEligibleDateMessage: nextEligibleMsg,
        );
      }

      // Check if there was a recent breeding failure (within 21 days)
      if (latestStatus == 'failed' && daysSinceLastBreeding < 21) {
        final nextEligibleDate = latestBreedingDate.add(const Duration(days: 21));
        return EligibilityCheckResult(
          isEligible: false,
          statusMessage: 'Not Recommended',
          reasonMessage: 'Recent breeding failure (${daysSinceLastBreeding.toString()} days ago)',
          statusColor: const Color(0xFF2196F3), // Blue
          statusIcon: Icons.warning,
          nextEligibleDateMessage: 'Recommended to wait until ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}',
        );
      }

      // For buffaloes: 2 months after last breeding (regardless of outcome)
      if (animalTypeId.toLowerCase().contains('buffalo') && daysSinceLastBreeding < 60) {
        final nextEligibleDate = latestBreedingDate.add(const Duration(days: 60));
        return EligibilityCheckResult(
          isEligible: false,
          statusMessage: 'In Empty Period',
          reasonMessage: 'Buffaloes need 2 months between breeding attempts',
          statusColor: const Color(0xFF2196F3), // Blue
          statusIcon: Icons.hourglass_empty,
          nextEligibleDateMessage: 'Will be eligible for breeding after ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}',
        );
      }
    }

    // Check post-calving empty period
    debugPrint('🔍 Breeding eligibility check - lastCalvingDate: $lastCalvingDate, cattleId: $cattleId');
    if (lastCalvingDate != null) {
      final daysSinceCalving = now.difference(lastCalvingDate).inDays;

      // Use animal type empty period or fallback based on animal type
      final requiredEmptyDays = animalTypeEmptyPeriodDays ??
          (animalTypeId.toLowerCase().contains('buffalo') ? 90 : 60);
      debugPrint('🔍 Days since calving: $daysSinceCalving, required empty days: $requiredEmptyDays');

      if (daysSinceCalving < requiredEmptyDays) {
        final nextEligibleDate = lastCalvingDate.add(Duration(days: requiredEmptyDays));
        final animalType = animalTypeId.toLowerCase().contains('buffalo') ? 'Buffaloes' : 'Cattle';
        final nextEligibleMsg = 'Will be eligible for breeding after ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}';

        debugPrint('🔍 Cattle $cattleId is in empty period - returning not eligible');
        return EligibilityCheckResult(
          isEligible: false,
          statusMessage: 'In Empty Period',
          reasonMessage: '$animalType need ${requiredEmptyDays ~/ 30} months rest after calving',
          statusColor: const Color(0xFF2196F3), // Blue
          statusIcon: Icons.hourglass_empty,
          nextEligibleDateMessage: nextEligibleMsg,
        );
      }
    } else {
      debugPrint('🔍 No lastCalvingDate found for cattle $cattleId');
    }

    // All checks passed - eligible
    return const EligibilityCheckResult(
      isEligible: true,
      statusMessage: 'Eligible for Breeding',
      reasonMessage: 'All requirements are met',
      statusColor: Color(0xFF4CAF50), // Green
      statusIcon: Icons.check_circle,
    );
  }

  /// Static method to check pregnancy eligibility
  static EligibilityCheckResult checkPregnancyEligibility({
    required String gender,
    required String cattleId,
    String? animalTypeId,
    required bool isPregnant,
    required DateTime? dateOfBirth,
    DateTime? purchaseDate,
    DateTime? lastCalvingDate,
    int? animalTypeEmptyPeriodDays,
  }) {
    // Get current date
    final now = DateTime.now();

    // Check gender (only females can be pregnant)
    if (gender.toLowerCase() != 'female') {
      return const EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Not Eligible',
        reasonMessage: 'Only female cattle can be recorded as pregnant',
        statusColor: Color(0xFFF44336), // Red
        statusIcon: Icons.block,
      );
    }

    // Check if already pregnant
    if (isPregnant) {
      return const EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Already Pregnant',
        reasonMessage: 'Pregnancy already recorded for this animal',
        statusColor: Color(0xFFF44336), // Red
        statusIcon: Icons.pregnant_woman,
      );
    }

    // Check age eligibility
    // If no date of birth and no purchase date, can't determine age
    if (dateOfBirth == null && purchaseDate == null) {
      return const EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Cannot Determine Eligibility',
        reasonMessage: 'Age information missing',
        statusColor: Color(0xFFFF9800), // Orange
        statusIcon: Icons.warning,
      );
    }

    // Calculate age
    int ageInMonths;
    bool isEstimated = false;
    const int minimumPregnancyAgeMonths = 18; // Standard minimum for pregnancy

    if (dateOfBirth != null) {
      ageInMonths = ((now.year - dateOfBirth.year) * 12) +
          (now.month - dateOfBirth.month);
    } else {
      // For purchased animals, use purchase date with estimated age at purchase
      const estimatedMonthsAtPurchase = 24; // Assume 2 years at purchase
      final monthsSincePurchase = ((now.year - purchaseDate!.year) * 12) +
          (now.month - purchaseDate.month);
      ageInMonths = estimatedMonthsAtPurchase + monthsSincePurchase;
      isEstimated = true;
    }

    // Return age ineligibility if too young
    if (ageInMonths < minimumPregnancyAgeMonths) {
      final monthsRemaining = minimumPregnancyAgeMonths - ageInMonths;
      final eligibleDate =
          DateTime(now.year, now.month + monthsRemaining, now.day);

      return EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Not Eligible - Too Young',
        reasonMessage:
            'Age: $ageInMonths months${isEstimated ? " (estimated)" : ""} (minimum: $minimumPregnancyAgeMonths months)',
        statusColor: const Color(0xFFF44336), // Red
        statusIcon: Icons.child_care,
        nextEligibleDateMessage:
            'Will be eligible after ${DateFormat('MMMM dd, yyyy').format(eligibleDate)}',
      );
    }

    // Check post-calving empty period (same as breeding eligibility)
    debugPrint('🔍 Pregnancy eligibility check - lastCalvingDate: $lastCalvingDate, cattleId: $cattleId');
    if (lastCalvingDate != null) {
      final daysSinceCalving = now.difference(lastCalvingDate).inDays;

      // Use animal type empty period or fallback based on animal type
      final requiredEmptyDays = animalTypeEmptyPeriodDays ??
          (animalTypeId?.toLowerCase().contains('buffalo') == true ? 90 : 60);
      debugPrint('🔍 Days since calving: $daysSinceCalving, required empty days: $requiredEmptyDays');

      if (daysSinceCalving < requiredEmptyDays) {
        // Calculate when the cattle will be eligible again
        final nextEligibleDate =
            lastCalvingDate.add(Duration(days: requiredEmptyDays));
        final nextEligibleMsg =
            'Will be eligible for pregnancy recording after ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}';
        final animalType = animalTypeId?.toLowerCase().contains('buffalo') == true
            ? 'Buffaloes'
            : 'Cattle';

        debugPrint('🔍 Cattle $cattleId is in empty period - returning not eligible');
        return EligibilityCheckResult(
          isEligible: false,
          statusMessage: 'In Empty Period',
          reasonMessage:
              '$animalType need ${requiredEmptyDays ~/ 30} months rest after calving',
          statusColor: const Color(0xFF2196F3), // Blue
          statusIcon: Icons.hourglass_empty,
          nextEligibleDateMessage: nextEligibleMsg,
        );
      }
    } else {
      debugPrint('🔍 No lastCalvingDate found for cattle $cattleId');
    }

    // If we've passed all checks, the animal is eligible
    return const EligibilityCheckResult(
      isEligible: true,
      statusMessage: 'Eligible for Pregnancy Recording',
      reasonMessage: 'All requirements are met',
      statusColor: Color(0xFF4CAF50), // Green
      statusIcon: Icons.check_circle,
    );
  }
}

// Helper class to store eligibility status information
class _EligibilityStatus {
  final bool isEligible;
  final String statusText;
  final String reasonText;
  final Color color;
  final IconData icon;
  final String? nextEligibleDateMessage;

  _EligibilityStatus({
    required this.isEligible,
    required this.statusText,
    required this.reasonText,
    required this.color,
    required this.icon,
    this.nextEligibleDateMessage,
  });
}
