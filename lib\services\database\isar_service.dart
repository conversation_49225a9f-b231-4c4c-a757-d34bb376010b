import 'dart:io';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logging/logging.dart';
import '../../Dashboard/Cattle/models/cattle_isar.dart';
import '../../Dashboard/Farm Setup/models/animal_type_isar.dart';
import '../../Dashboard/Farm Setup/models/breed_category_isar.dart';
import '../../Dashboard/Farm Setup/models/farm_isar.dart';
import '../../Dashboard/Farm Setup/models/user_role_isar.dart';
import '../../Dashboard/Breeding/models/breeding_record_isar.dart';
import '../../Dashboard/Breeding/models/pregnancy_record_isar.dart';
import '../../Dashboard/Breeding/models/breeding_event_isar.dart';
import '../../Dashboard/Breeding/models/delivery_record_isar.dart';
import '../../Dashboard/Transactions/models/transaction_isar.dart';
import '../../Dashboard/Transactions/models/category_isar.dart';
import '../../Dashboard/Health/models/health_record_isar.dart';
import '../../Dashboard/Health/models/vaccination_record_isar.dart';
import '../../Dashboard/Milk Records/models/milk_record_isar.dart';
import '../../Dashboard/Milk Records/models/milk_sale_isar.dart';
import '../../Dashboard/Weight/models/weight_record.dart';
import '../../Dashboard/Notifications/models/notification_isar.dart';
import '../../Dashboard/Notifications/models/notification_settings_isar.dart';
import '../../Dashboard/Events/models/event_type_isar.dart';
import '../../Dashboard/Events/models/event_isar.dart';
import '../../Dashboard/Farm Setup/models/backup_settings_isar.dart';
import '../../Dashboard/Farm Setup/models/currency_settings_isar.dart';
import '../../Dashboard/Farm Setup/models/milk_settings_isar.dart';
import '../../Dashboard/Health/models/medication_isar.dart';
import '../../Dashboard/Health/models/treatment_isar.dart';
import '../../Dashboard/Farm Setup/models/alert_settings_isar.dart';
import '../../Dashboard/Farm Setup/models/farm_user_isar.dart';
// Temporarily commented out until properly generated
// import '../../Dashboard/Farm Setup/models/animal_stage_isar.dart';

// Generated schema files are imported through their main dart files

/// Service to manage the Isar database instance
class IsarService {
  final Logger _logger = Logger('IsarService');
  late final Isar _isar;
  static const String _databaseName = 'cattle_manager_db';
  bool _isInitialized = false;

  // Singleton instance
  static IsarService? _instance;

  // Private constructor for singleton
  IsarService._();

  // Getter for singleton instance
  static Future<IsarService> get instance async {
    if (_instance == null) {
      _instance = IsarService._();
      await _instance!.initialize();
    }
    return _instance!;
  }

  /// Check if Isar has been initialized
  bool get isInitialized => _isInitialized;

  /// Get the Isar instance
  Isar get isar {
    if (!_isInitialized) {
      throw Exception('Isar database not initialized');
    }
    return _isar;
  }

  /// Initialize Isar database
  Future<bool> initialize() async {
    if (_isInitialized) {
      _logger.info('Isar database already initialized');
      return true;
    }

    try {
      final dir = await getApplicationDocumentsDirectory();
      _logger.info('Initializing Isar database at ${dir.path}');

      // You need to run 'flutter pub run build_runner build' to generate the schemas
      _isar = await Isar.open(
        [
          CattleIsarSchema,
          AnimalTypeIsarSchema,
          BreedCategoryIsarSchema,
          // Temporarily commented out until properly generated
          // AnimalStageIsarSchema,
          FarmIsarSchema,
          UserRoleIsarSchema,
          BreedingRecordIsarSchema,
          PregnancyRecordIsarSchema,
          BreedingEventIsarSchema,
          DeliveryRecordIsarSchema,
          TransactionIsarSchema,
          CategoryIsarSchema,
          HealthRecordIsarSchema,
          VaccinationIsarSchema,
          MedicationIsarSchema,
          TreatmentIsarSchema,
          MilkRecordIsarSchema,
          MilkSaleIsarSchema,
          WeightRecordSchema,
          NotificationIsarSchema,
          NotificationSettingsIsarSchema,
          EventTypeIsarSchema,
          EventIsarSchema,
          BackupSettingsIsarSchema,
          CurrencySettingsIsarSchema,
          MilkSettingsIsarSchema,
          AlertSettingsIsarSchema,
          FarmUserIsarSchema
        ],
        directory: dir.path,
        name: _databaseName,
        inspector: true, // Enable inspector in debug mode
      );

      _isInitialized = true;
      _logger.info('Isar database initialized successfully');
      return true;
    } catch (e) {
      _logger.severe('Error initializing Isar database: $e');
      return false;
    }
  }

  /// Close the Isar database instance
  Future<void> close() async {
    if (_isInitialized) {
      try {
        await _isar.close();
        _isInitialized = false;
        _logger.info('Isar database closed successfully');
      } catch (e) {
        _logger.severe('Error closing Isar database: $e');
        rethrow;
      }
    }
  }

  /// Clear all data from the database
  Future<void> clearAllData() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _isar.writeTxn(() async {
        await _isar.clear();
      });
      _logger.info('All data cleared from Isar database');
    } catch (e) {
      _logger.severe('Error clearing all data from Isar database: $e');
      rethrow;
    }
  }

  /// Create a backup of the database
  Future<File> backup() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final appDir = await getApplicationDocumentsDirectory();
      final now = DateTime.now();
      final formattedDate =
          '${now.year}-${now.month}-${now.day}-${now.hour}-${now.minute}';
      final backupPath = '${appDir.path}/backup_$formattedDate.isar';

      // Close the current instance to ensure all data is written
      await _isar.close();
      _isInitialized = false;

      // Copy the database file to the backup location
      final dbFile = File('${appDir.path}/$_databaseName.isar');
      final backupFile = await dbFile.copy(backupPath);

      // Re-open the database
      await initialize();

      _logger.info('Database backup created at: $backupPath');
      return backupFile;
    } catch (e) {
      _logger.severe('Error creating database backup: $e');
      // Make sure to reinitialize if there was an error
      if (!_isInitialized) {
        await initialize();
      }
      rethrow;
    }
  }

  /// Restore from a backup file
  Future<void> restoreFromBackup(File backupFile) async {
    try {
      // Close the current instance if it's open
      if (_isInitialized) {
        await _isar.close();
        _isInitialized = false;
      }

      final appDir = await getApplicationDocumentsDirectory();
      final dbFilePath = '${appDir.path}/$_databaseName.isar';

      // Copy the backup file to the database location
      await backupFile.copy(dbFilePath);

      // Reopen the database
      await initialize();

      _logger.info('Database restored from backup: ${backupFile.path}');
    } catch (e) {
      _logger.severe('Error restoring database from backup: $e');
      // Make sure to reinitialize if there was an error
      if (!_isInitialized) {
        await initialize();
      }
      rethrow;
    }
  }

  // Isar getter is already defined above

  // Collection accessors
  IsarCollection<CattleIsar> get cattleIsars => _isar.collection<CattleIsar>();
  IsarCollection<AnimalTypeIsar> get animalTypeIsars => _isar.collection<AnimalTypeIsar>();
  IsarCollection<BreedCategoryIsar> get breedCategoryIsars => _isar.collection<BreedCategoryIsar>();
  IsarCollection<FarmIsar> get farmIsars => _isar.collection<FarmIsar>();
  IsarCollection<UserRoleIsar> get userRoleIsars => _isar.collection<UserRoleIsar>();
  IsarCollection<BreedingRecordIsar> get breedingRecordIsars => _isar.collection<BreedingRecordIsar>();
  IsarCollection<PregnancyRecordIsar> get pregnancyRecordIsars => _isar.collection<PregnancyRecordIsar>();
  IsarCollection<BreedingEventIsar> get breedingEventIsars => _isar.collection<BreedingEventIsar>();
  IsarCollection<DeliveryRecordIsar> get deliveryRecordIsars => _isar.collection<DeliveryRecordIsar>();
  IsarCollection<TransactionIsar> get transactionIsars => _isar.collection<TransactionIsar>();
  IsarCollection<CategoryIsar> get categoryIsars => _isar.collection<CategoryIsar>();
  IsarCollection<HealthRecordIsar> get healthRecordIsars => _isar.collection<HealthRecordIsar>();
  IsarCollection<VaccinationIsar> get vaccinationIsars => _isar.collection<VaccinationIsar>();
  IsarCollection<MedicationIsar> get medicationIsars => _isar.collection<MedicationIsar>();
  IsarCollection<TreatmentIsar> get treatmentIsars => _isar.collection<TreatmentIsar>();
  IsarCollection<MilkRecordIsar> get milkRecordIsars => _isar.collection<MilkRecordIsar>();
  IsarCollection<MilkSaleIsar> get milkSaleIsars => _isar.collection<MilkSaleIsar>();
  IsarCollection<WeightRecord> get weightRecords => _isar.collection<WeightRecord>();
  IsarCollection<NotificationIsar> get notificationIsars => _isar.collection<NotificationIsar>();
  IsarCollection<NotificationSettingsIsar> get notificationSettingsIsars => _isar.collection<NotificationSettingsIsar>();
  IsarCollection<EventTypeIsar> get eventTypeIsars => _isar.collection<EventTypeIsar>();
  IsarCollection<EventIsar> get eventIsars => _isar.collection<EventIsar>();
  IsarCollection<BackupSettingsIsar> get backupSettingsIsars => _isar.collection<BackupSettingsIsar>();
  IsarCollection<CurrencySettingsIsar> get currencySettingsIsars => _isar.collection<CurrencySettingsIsar>();
  IsarCollection<MilkSettingsIsar> get milkSettingsIsars => _isar.collection<MilkSettingsIsar>();
  IsarCollection<AlertSettingsIsar> get alertSettingsIsars => _isar.collection<AlertSettingsIsar>();
  IsarCollection<FarmUserIsar> get farmUserIsars => _isar.collection<FarmUserIsar>();
  // Temporarily commented out until properly generated
  // IsarCollection<AnimalStageIsar> get animalStageIsars => _isar.collection<AnimalStageIsar>();
}
