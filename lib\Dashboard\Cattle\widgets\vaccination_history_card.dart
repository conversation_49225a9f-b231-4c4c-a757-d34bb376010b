import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';

class VaccinationHistoryCard extends StatelessWidget {
  final List<Map<String, dynamic>> records;
  final String title;
  final String emptyMessage;
  final Function(Map<String, dynamic>)? onEdit;
  final Function(Map<String, dynamic>)? onDelete;
  final Function(Map<String, dynamic>)? onStatusTap;
  final Function(Map<String, dynamic>)? onCattleTap; // Add cattle tap callback
  // Optional cattle information - if provided, it will be shown in record headers
  final String? cattleName;
  final String? cattleId;

  const VaccinationHistoryCard({
    super.key,
    required this.records,
    this.title = 'Vaccination History',
    this.emptyMessage = 'No vaccination records found',
    this.onEdit,
    this.onDelete,
    this.onStatusTap,
    this.onCattleTap, // Add cattle tap callback
    this.cattleName,
    this.cattleId,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.purple.withAlpha(26), // Light background
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.teal.withAlpha(51),
                  child: const Icon(
                    Icons.vaccines,
                    color: Colors.teal,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal,
                    ),
                  ),
                ),
                Text(
                  '${records.length} ${records.length == 1 ? 'vaccination' : 'vaccinations'}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.purple[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Content
          if (records.isEmpty)
            Padding(
              padding: const EdgeInsets.all(32),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.vaccines_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      emptyMessage,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black54,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add a vaccination record to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Flexible(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(10, 16, 10, 16),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: records.length,
                  itemBuilder: (context, index) {
                    // Add divider between items
                    if (index > 0) {
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Divider(height: 32),
                          _buildVaccinationRecord(context, records[index]),
                        ],
                      );
                    }
                    return _buildVaccinationRecord(context, records[index]);
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVaccinationRecord(BuildContext context, Map<String, dynamic> record) {
    // Extract record data with null safety
    final vaccinationDate = DateTime.tryParse(record['vaccinationDate']?.toString() ?? record['date']?.toString() ?? '') ?? DateTime.now();
    final nextDueDate = DateTime.tryParse(record['nextDueDate']?.toString() ?? '');
    final vaccineName = record['vaccineName']?.toString() ?? record['name']?.toString() ?? 'Unknown vaccine';
    final manufacturer = record['manufacturer']?.toString() ?? 'Not specified';
    final batchNumber = record['batchNumber']?.toString() ?? 'Not specified';
    final dosage = record['dosage']?.toString() ?? 'Not specified';
    final veterinarian = record['veterinarian']?.toString() ?? 'Not specified';
    final cost = record['cost']?.toString() ?? '0.00';
    final notes = record['notes']?.toString() ?? '';
    final status = record['status']?.toString() ?? 'Completed';
    
    // Use record's cattle info with fallback to the widget's cattle info
    final recordCattleId = record['cattleId']?.toString() ?? '';
    final recordCattleName = record['cattleName']?.toString() ?? '';
    final displayCattleId = recordCattleId.isNotEmpty ? recordCattleId : cattleId ?? '';
    final displayCattleName = recordCattleName.isNotEmpty ? recordCattleName : cattleName ?? '';

    // Determine status color
    Color statusColor;
    switch (status.toLowerCase()) {
      case 'completed':
        statusColor = Colors.green;
        break;
      case 'due':
      case 'overdue':
        statusColor = Colors.red;
        break;
      case 'scheduled':
        statusColor = Colors.blue;
        break;
      default:
        statusColor = Colors.purple;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Record Header with status-based color - make entire header tappable
        GestureDetector(
          onTap: onCattleTap != null ? () => onCattleTap!(record) : null,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColor.withAlpha(51), // 0.2 opacity (standardized)
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: statusColor.withAlpha(70), // 0.27 opacity (standardized)
                        child: Icon(
                          Icons.vaccines,
                          color: statusColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('MMMM dd, yyyy').format(vaccinationDate),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold, // Standardized
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (displayCattleId.isNotEmpty || displayCattleName.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                '${displayCattleName.isNotEmpty ? displayCattleName : 'Unknown'} (${displayCattleId.isNotEmpty ? displayCattleId : 'Unknown'})',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600, // Standardized
                                  color: statusColor,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // More options menu (edit/delete)
                if (onEdit != null || onDelete != null)
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) {
                      if (value == 'edit' && onEdit != null) {
                        onEdit!(record);
                      } else if (value == 'delete' && onDelete != null) {
                        onDelete!(record);
                      }
                    },
                    itemBuilder: (context) => [
                      if (onEdit != null)
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                      if (onDelete != null)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
              ],
            ),
          ),
        ),

        // Record Details
        Padding(
          padding: const EdgeInsets.all(16), // Standardized padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Vaccine Name
              InfoRow(
                icon: Icons.vaccines,
                label: 'Vaccine',
                value: vaccineName,
                color: Colors.purple,
              ),
              const SizedBox(height: 10),

              // Status
              InfoRow(
                icon: Icons.check_circle_outline,
                label: 'Status',
                value: status,
                color: statusColor,
                isStatus: true,
                onTap: onStatusTap != null ? () => onStatusTap!(record) : null,
              ),
              const SizedBox(height: 10),

              // Manufacturer
              InfoRow(
                icon: Icons.business,
                label: 'Manufacturer',
                value: manufacturer,
                color: Colors.blue,
              ),
              const SizedBox(height: 10),

              // Batch Number & Dosage
              InfoRow(
                icon: Icons.inventory,
                label: 'Batch & Dosage',
                value: '$batchNumber - $dosage',
                color: Colors.teal,
              ),
              const SizedBox(height: 10),

              // Next Due Date
              if (nextDueDate != null)
                InfoRow(
                  icon: Icons.schedule,
                  label: 'Next Due',
                  value: DateFormat('MMMM dd, yyyy').format(nextDueDate),
                  color: Colors.orange,
                ),
              if (nextDueDate != null) const SizedBox(height: 10),

              // Veterinarian
              InfoRow(
                icon: Icons.person,
                label: 'Veterinarian',
                value: veterinarian,
                color: Colors.indigo,
              ),
              const SizedBox(height: 10),

              // Cost
              InfoRow(
                icon: Icons.monetization_on_outlined,
                label: 'Cost',
                value: '\$$cost',
                color: Colors.green,
              ),

              // Notes section
              if (notes.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 4),
                InfoRow(
                  icon: Icons.notes,
                  label: 'Notes',
                  value: notes,
                  color: Colors.deepPurple,
                  isMultiline: true,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
