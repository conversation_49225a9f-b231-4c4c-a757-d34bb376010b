import 'package:isar/isar.dart';

part 'treatment_isar.g.dart';

@Collection()
class TreatmentIsar {
  Id id = Isar.autoIncrement;
  
  String? cattleId;
  String? treatment;
  String? condition;
  String? notes;
  DateTime? date;
  String? followUpDate;
  String? cost;
  String? veterinarian;
  String? outcome;
  String? businessId;
  DateTime? createdAt;
  DateTime? updatedAt;
  bool? isArchived;
  String? status;
  String? dosage;

  TreatmentIsar({
    this.cattleId,
    this.treatment,
    this.condition,
    this.notes,
    this.date,
    this.followUpDate,
    this.cost,
    this.veterinarian,
    this.outcome,
    this.businessId,
    this.createdAt,
    this.updatedAt,
    this.isArchived = false,
    this.status,
    this.dosage,
  });

  /// Convert to a Map for serialization
  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleId': cattleId,
      'treatment': treatment,
      'condition': condition,
      'notes': notes,
      'date': date?.toIso8601String(),
      'followUpDate': followUpDate,
      'cost': cost,
      'veterinarian': veterinarian,
      'outcome': outcome,
      'businessId': businessId,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isArchived': isArchived,
      'status': status,
      'dosage': dosage,
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory TreatmentIsar.fromMap(Map<String, dynamic> map) {
    return TreatmentIsar(
      cattleId: map['cattleId'] as String?,
      treatment: map['treatment'] as String?,
      condition: map['condition'] as String?,
      notes: map['notes'] as String?,
      date: map['date'] != null ? DateTime.parse(map['date'].toString()) : null,
      followUpDate: map['followUpDate'] as String?,
      cost: map['cost'] as String?,
      veterinarian: map['veterinarian'] as String?,
      outcome: map['outcome'] as String?,
      businessId: map['businessId'] as String? ?? map['id'] as String?,
      createdAt: map['createdAt'] != null ? DateTime.parse(map['createdAt'].toString()) : DateTime.now(),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt'].toString()) : DateTime.now(),
      isArchived: map['isArchived'] as bool? ?? false,
      status: map['status'] as String?,
      dosage: map['dosage'] as String?,
    );
  }

  factory TreatmentIsar.fromJson(Map<String, dynamic> json) => TreatmentIsar.fromMap(json);
}